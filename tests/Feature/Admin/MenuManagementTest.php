<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class MenuManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['email' => '<EMAIL>']);
        $this->user = User::factory()->create(['email' => '<EMAIL>']);
    }

    public function test_admin_can_access_menus_index()
    {
        $response = $this->actingAs($this->admin)->get('/admin/menus');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/Menus/Index'));
    }

    public function test_non_admin_cannot_access_menus_index()
    {
        $response = $this->actingAs($this->user)->get('/admin/menus');

        $response->assertStatus(403);
    }

    public function test_admin_can_access_create_menu()
    {
        $response = $this->actingAs($this->admin)->get('/admin/menus/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/Menus/Create'));
    }

    public function test_admin_can_create_menu()
    {
        $menuData = [
            'name' => 'Test Menu',
            'location' => 'header',
            'description' => 'A test menu',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post('/admin/menus', $menuData);

        $response->assertRedirect();
        $this->assertDatabaseHas('menus', $menuData);
    }

    public function test_menu_creation_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)->post('/admin/menus', []);

        $response->assertSessionHasErrors(['name', 'location']);
    }

    public function test_menu_creation_validates_location()
    {
        $response = $this->actingAs($this->admin)->post('/admin/menus', [
            'name' => 'Test Menu',
            'location' => 'invalid_location',
        ]);

        $response->assertSessionHasErrors(['location']);
    }

    public function test_admin_can_view_menu_edit_form()
    {
        $menu = Menu::factory()->create();

        $response = $this->actingAs($this->admin)->get("/admin/menus/{$menu->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/Menus/Edit'));
    }

    public function test_admin_can_update_menu()
    {
        $menu = Menu::factory()->create();
        $updateData = [
            'name' => 'Updated Menu Name',
            'location' => 'footer',
            'description' => 'Updated description',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->admin)->put("/admin/menus/{$menu->id}", $updateData);

        $response->assertRedirect('/admin/menus');
        $this->assertDatabaseHas('menus', array_merge(['id' => $menu->id], $updateData));
    }

    public function test_admin_can_delete_menu()
    {
        $menu = Menu::factory()->create();

        $response = $this->actingAs($this->admin)->delete("/admin/menus/{$menu->id}");

        $response->assertRedirect('/admin/menus');
        $this->assertDatabaseMissing('menus', ['id' => $menu->id]);
    }

    public function test_admin_can_view_menu_with_items()
    {
        $menu = Menu::factory()->create();
        MenuItem::factory()->count(3)->create(['menu_id' => $menu->id]);

        $response = $this->actingAs($this->admin)->get("/admin/menus/{$menu->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/Menus/Show'));
    }

    public function test_admin_can_add_custom_menu_item()
    {
        $menu = Menu::factory()->create();
        $itemData = [
            'title' => 'Test Link',
            'type' => 'custom',
            'url' => 'https://example.com',
            'target' => '_blank',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post("/admin/menus/{$menu->id}/items", $itemData);

        $response->assertRedirect();
        $this->assertDatabaseHas('menu_items', array_merge($itemData, ['menu_id' => $menu->id]));
    }

    public function test_admin_can_add_page_menu_item()
    {
        $menu = Menu::factory()->create();
        $page = Page::factory()->create();
        $itemData = [
            'title' => 'Page Link',
            'type' => 'page',
            'reference_id' => $page->id,
            'target' => '_self',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post("/admin/menus/{$menu->id}/items", $itemData);

        $response->assertRedirect();
        $this->assertDatabaseHas('menu_items', array_merge($itemData, ['menu_id' => $menu->id]));
    }

    public function test_admin_can_update_menu_item()
    {
        $menu = Menu::factory()->create();
        $menuItem = MenuItem::factory()->create(['menu_id' => $menu->id]);
        $updateData = [
            'title' => 'Updated Item Title',
            'type' => 'custom',
            'url' => 'https://updated.com',
            'target' => '_blank',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->admin)->put("/admin/menus/{$menu->id}/items/{$menuItem->id}", $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('menu_items', array_merge(['id' => $menuItem->id], $updateData));
    }

    public function test_admin_can_delete_menu_item()
    {
        $menu = Menu::factory()->create();
        $menuItem = MenuItem::factory()->create(['menu_id' => $menu->id]);

        $response = $this->actingAs($this->admin)->delete("/admin/menus/{$menu->id}/items/{$menuItem->id}");

        $response->assertRedirect();
        $this->assertDatabaseMissing('menu_items', ['id' => $menuItem->id]);
    }

    public function test_menu_item_validates_required_fields()
    {
        $menu = Menu::factory()->create();

        $response = $this->actingAs($this->admin)->post("/admin/menus/{$menu->id}/items", []);

        $response->assertSessionHasErrors(['title', 'type']);
    }

    public function test_custom_menu_item_requires_url()
    {
        $menu = Menu::factory()->create();

        $response = $this->actingAs($this->admin)->post("/admin/menus/{$menu->id}/items", [
            'title' => 'Test Link',
            'type' => 'custom',
            'target' => '_self',
        ]);

        $response->assertSessionHasErrors(['url']);
    }

    public function test_menu_items_are_ordered_correctly()
    {
        $menu = Menu::factory()->create();
        $item1 = MenuItem::factory()->create(['menu_id' => $menu->id, 'order' => 2]);
        $item2 = MenuItem::factory()->create(['menu_id' => $menu->id, 'order' => 1]);
        $item3 = MenuItem::factory()->create(['menu_id' => $menu->id, 'order' => 3]);

        $menu->refresh();
        $orderedItems = $menu->items()->orderBy('order')->get();

        $this->assertEquals($item2->id, $orderedItems[0]->id);
        $this->assertEquals($item1->id, $orderedItems[1]->id);
        $this->assertEquals($item3->id, $orderedItems[2]->id);
    }

    public function test_menu_item_can_have_parent()
    {
        $menu = Menu::factory()->create();
        $parentItem = MenuItem::factory()->create(['menu_id' => $menu->id]);
        $childItem = MenuItem::factory()->create([
            'menu_id' => $menu->id,
            'parent_id' => $parentItem->id,
        ]);

        $this->assertEquals($parentItem->id, $childItem->parent_id);
        $this->assertTrue($parentItem->children->contains($childItem));
    }

    public function test_menu_caching_works()
    {
        $menu = Menu::factory()->active()->header()->create();
        MenuItem::factory()->count(2)->create(['menu_id' => $menu->id]);

        // First call should cache the menu
        $cachedMenu = Menu::getCachedByLocation('header');
        $this->assertNotNull($cachedMenu);
        $this->assertEquals($menu->id, $cachedMenu->id);

        // Second call should return cached version
        $cachedMenu2 = Menu::getCachedByLocation('header');
        $this->assertEquals($cachedMenu->id, $cachedMenu2->id);
    }

    public function test_menu_cache_is_cleared_when_menu_updated()
    {
        $menu = Menu::factory()->active()->header()->create();
        
        // Cache the menu
        Menu::getCachedByLocation('header');
        
        // Update the menu (should clear cache)
        $menu->update(['name' => 'Updated Name']);
        
        // Cache should be cleared and menu should be re-cached with new data
        $cachedMenu = Menu::getCachedByLocation('header');
        $this->assertEquals('Updated Name', $cachedMenu->name);
    }

    public function test_menu_filtering_works()
    {
        $headerMenu = Menu::factory()->header()->create(['name' => 'Header Menu']);
        $footerMenu = Menu::factory()->footer()->create(['name' => 'Footer Menu']);

        $response = $this->actingAs($this->admin)->get('/admin/menus?location=header');

        $response->assertStatus(200);
        // The response should only contain the header menu
    }

    public function test_menu_search_works()
    {
        $menu1 = Menu::factory()->create(['name' => 'Main Navigation']);
        $menu2 = Menu::factory()->create(['name' => 'Footer Links']);

        $response = $this->actingAs($this->admin)->get('/admin/menus?search=Navigation');

        $response->assertStatus(200);
        // The response should only contain the main navigation menu
    }

    public function test_deleting_menu_cascades_to_items()
    {
        $menu = Menu::factory()->create();
        $menuItem = MenuItem::factory()->create(['menu_id' => $menu->id]);

        $menu->delete();

        $this->assertDatabaseMissing('menus', ['id' => $menu->id]);
        $this->assertDatabaseMissing('menu_items', ['id' => $menuItem->id]);
    }

    public function test_deleting_parent_menu_item_nullifies_children()
    {
        $menu = Menu::factory()->create();
        $parentItem = MenuItem::factory()->create(['menu_id' => $menu->id]);
        $childItem = MenuItem::factory()->create([
            'menu_id' => $menu->id,
            'parent_id' => $parentItem->id,
        ]);

        $parentItem->delete();

        $childItem->refresh();
        $this->assertNull($childItem->parent_id);
    }
}
