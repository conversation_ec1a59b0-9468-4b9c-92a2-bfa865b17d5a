<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SubscriptionCreationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;
    protected $user;
    protected $premiumPlan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create test user
        $this->user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);
        
        // Create premium pricing plan
        $this->premiumPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 29.99,
            'currency' => 'USD',
            'interval' => 'month',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_create_premium_subscription_successfully()
    {
        $subscription = $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'test_subscription_id',
            'paddle'
        );

        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals($this->user->id, $subscription->user_id);
        $this->assertEquals('premium', $subscription->plan_name);
        $this->assertEquals($this->premiumPlan->id, $subscription->pricing_plan_id);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals('paddle', $subscription->payment_gateway);
        $this->assertEquals('test_subscription_id', $subscription->paddle_subscription_id);
        
        // Check user subscription plan was updated
        $this->user->refresh();
        $this->assertEquals('premium', $this->user->subscription_plan);
    }

    /** @test */
    public function it_cancels_existing_active_subscriptions_when_creating_new_one()
    {
        // Create existing active subscription
        $existingSubscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
        ]);

        // Create new subscription
        $newSubscription = $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'new_subscription_id',
            'shurjopay'
        );

        // Check existing subscription was cancelled
        $existingSubscription->refresh();
        $this->assertEquals('cancelled', $existingSubscription->status);
        
        // Check new subscription is active
        $this->assertEquals('active', $newSubscription->status);
        $this->assertEquals('shurjopay', $newSubscription->payment_gateway);
    }

    /** @test */
    public function it_throws_exception_when_premium_plan_not_found()
    {
        // Delete the premium plan
        $this->premiumPlan->delete();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Premium plan not found or not active');

        $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'test_subscription_id',
            'paddle'
        );
    }

    /** @test */
    public function it_throws_exception_when_premium_plan_is_inactive()
    {
        // Make premium plan inactive
        $this->premiumPlan->update(['is_active' => false]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Premium plan not found or not active');

        $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'test_subscription_id',
            'paddle'
        );
    }

    /** @test */
    public function it_throws_exception_for_invalid_user()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid user provided');

        $this->subscriptionService->createPremiumSubscription(
            null,
            'test_subscription_id',
            'paddle'
        );
    }

    /** @test */
    public function it_handles_different_payment_gateways_correctly()
    {
        $gateways = ['paddle', 'shurjopay', 'coinbase_commerce', 'offline'];

        foreach ($gateways as $gateway) {
            $user = User::factory()->create(['subscription_plan' => 'free']);
            
            $subscription = $this->subscriptionService->createPremiumSubscription(
                $user,
                'test_' . $gateway . '_id',
                $gateway
            );

            $this->assertEquals($gateway, $subscription->payment_gateway);
            
            // Check gateway-specific subscription ID field
            switch ($gateway) {
                case 'paddle':
                    $this->assertEquals('test_paddle_id', $subscription->paddle_subscription_id);
                    break;
                case 'shurjopay':
                    $this->assertEquals('test_shurjopay_id', $subscription->shurjopay_subscription_id);
                    break;
                case 'coinbase_commerce':
                    $this->assertEquals('test_coinbase_commerce_id', $subscription->coinbase_commerce_subscription_id);
                    break;
            }
        }
    }

    /** @test */
    public function it_sets_correct_subscription_period()
    {
        $subscription = $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'test_subscription_id',
            'paddle'
        );

        $this->assertNotNull($subscription->current_period_start);
        $this->assertNotNull($subscription->current_period_end);
        
        // Check that end date is approximately one month from start date
        $expectedEndDate = $subscription->current_period_start->addMonth();
        $this->assertTrue(
            $subscription->current_period_end->diffInMinutes($expectedEndDate) < 5,
            'Subscription end date should be approximately one month from start date'
        );
    }

    /** @test */
    public function checkout_endpoint_creates_subscription_successfully()
    {
        $response = $this->actingAs($this->user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.checkout'), [
                'payment_gateway' => 'offline',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('subscription.dashboard'));
        $response->assertSessionHas('success', 'Premium subscription activated successfully!');

        // Check subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'payment_gateway' => 'offline',
        ]);
    }

    /** @test */
    public function checkout_endpoint_prevents_duplicate_subscriptions()
    {
        // Create existing active subscription and update user's subscription plan
        Subscription::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'current_period_end' => now()->addMonth(),
        ]);

        // Update user's subscription plan to premium
        $this->user->update(['subscription_plan' => 'premium']);

        $response = $this->actingAs($this->user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.checkout'), [
                'payment_gateway' => 'offline',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'You already have an active premium subscription.');
    }

    /** @test */
    public function checkout_endpoint_validates_input()
    {
        $response = $this->actingAs($this->user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.checkout'), [
                'payment_gateway' => 'invalid_gateway',
                '_token' => 'test-token'
            ]);

        $response->assertSessionHasErrors(['payment_gateway']);
    }

    /** @test */
    public function checkout_endpoint_handles_service_exceptions()
    {
        // Mock the subscription service to throw an exception
        $this->mock(SubscriptionService::class, function ($mock) {
            $mock->shouldReceive('createPremiumSubscription')
                 ->andThrow(new \Exception('Service error'));
        });

        $response = $this->actingAs($this->user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.checkout'), [
                'payment_gateway' => 'offline',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Failed to create subscription. Please try again or contact support.');
    }

    /** @test */
    public function it_logs_subscription_creation_events()
    {
        // Capture log messages
        $logMessages = [];
        \Log::listen(function ($event) use (&$logMessages) {
            $logMessages[] = [
                'level' => $event->level,
                'message' => $event->message,
                'context' => $event->context
            ];
        });

        $this->subscriptionService->createPremiumSubscription(
            $this->user,
            'test_subscription_id',
            'paddle'
        );

        // Check that log messages were created
        $this->assertNotEmpty($logMessages);

        // Check for specific log message
        $foundLogMessage = false;
        foreach ($logMessages as $log) {
            if (str_contains($log['message'], 'Premium subscription created successfully')) {
                $foundLogMessage = true;
                break;
            }
        }

        $this->assertTrue($foundLogMessage, 'Expected log message not found');
    }
}
