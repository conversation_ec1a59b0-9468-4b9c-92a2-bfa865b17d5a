<?php

namespace Tests\Feature;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use App\Models\User;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class RealEmailSendingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();
    }

    /**
     * Test that we can send emails using the current configuration.
     * This test uses the actual mail configuration from .env
     */
    public function test_can_send_email_with_current_configuration()
    {
        // Get current mail configuration
        $currentProvider = config('mail.default');
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');
        
        // Verify configuration is set
        $this->assertNotEmpty($currentProvider, 'Mail provider should be configured');
        $this->assertNotEmpty($fromAddress, 'From address should be configured');
        $this->assertNotEmpty($fromName, 'From name should be configured');
        
        // Test that we can create an EmailService instance
        $emailService = app(EmailService::class);
        $this->assertInstanceOf(EmailService::class, $emailService);
        
        // Test that the service recognizes the current provider
        $this->assertContains($currentProvider, ['smtp', 'sendgrid', 'log', 'array']);
        
        $this->assertTrue(true, 'Email configuration is valid and service can be instantiated');
    }

    /**
     * Test email sending with Log provider (safe for testing).
     */
    public function test_email_sending_with_log_provider()
    {
        // Temporarily set to log provider for safe testing
        Config::set('mail.default', 'log');
        
        $emailService = app(EmailService::class);
        $testEmail = '<EMAIL>';
        $mailable = new \App\Mail\TestEmail('Test message for log provider');
        
        // Capture initial state
        $initialLogCount = EmailLog::count();
        
        // Send email
        $result = $emailService->send($mailable, $testEmail, [
            'test_context' => 'log_provider_test',
        ]);
        
        // Verify sending was successful
        $this->assertTrue($result, 'Email should be sent successfully via log provider');
        
        // Verify email was logged
        $this->assertGreaterThan($initialLogCount, EmailLog::count(), 'Email should be logged');
        
        // Get the email log
        $emailLog = EmailLog::where('to_email', $testEmail)->first();
        $this->assertNotNull($emailLog, 'Email log should exist');
        $this->assertEquals('log', $emailLog->provider);
        $this->assertEquals($testEmail, $emailLog->to_email);
    }

    /**
     * Test email configuration validation.
     */
    public function test_email_configuration_validation()
    {
        $providers = ['smtp', 'sendgrid', 'log'];
        
        foreach ($providers as $provider) {
            Config::set('mail.default', $provider);
            
            // Test that configuration is recognized
            $this->assertEquals($provider, config('mail.default'));
            
            // Test that EmailService can handle this provider
            $emailService = app(EmailService::class);
            $this->assertInstanceOf(EmailService::class, $emailService);
            
            // Test that the service has the appropriate method
            $reflection = new \ReflectionClass($emailService);
            $methodName = 'sendVia' . ucfirst($provider === 'sendgrid' ? 'SendGrid' : ucfirst($provider));
            $this->assertTrue($reflection->hasMethod($methodName), 
                "EmailService should have {$methodName} method");
        }
    }

    /**
     * Test that email templates render correctly for real sending.
     */
    public function test_email_templates_render_for_real_sending()
    {
        $testCases = [
            [
                'template' => 'emails/test-email',
                'data' => [
                    'testMessage' => 'Real world test message',
                    'timestamp' => now()->format('Y-m-d H:i:s'),
                    'appName' => config('app.name'),
                    'appUrl' => config('app.url'),
                ],
            ],
        ];

        foreach ($testCases as $testCase) {
            // Test that template exists and can be rendered
            $this->assertFileExists(resource_path("views/{$testCase['template']}.blade.php"));
            
            // Test rendering
            $rendered = view($testCase['template'], $testCase['data'])->render();
            $this->assertIsString($rendered);
            $this->assertNotEmpty($rendered);
            
            // Test that data is properly interpolated
            $this->assertStringContainsString($testCase['data']['testMessage'], $rendered);
            $this->assertStringContainsString($testCase['data']['appName'], $rendered);
        }
    }

    /**
     * Test email tracking routes exist and are accessible.
     */
    public function test_email_tracking_routes_exist()
    {
        $trackingRoutes = [
            'email.track.open',
            'email.track.click',
        ];

        foreach ($trackingRoutes as $routeName) {
            // Test that route exists
            $this->assertTrue(
                \Illuminate\Support\Facades\Route::has($routeName),
                "Route {$routeName} should exist"
            );
            
            // Test that route can generate URLs
            $url = route($routeName, ['messageId' => 'test-123']);
            $this->assertIsString($url);
            $this->assertStringContainsString('test-123', $url);
        }
    }

    /**
     * Test that email sending works with authentication emails.
     */
    public function test_authentication_email_infrastructure()
    {
        // Create a test user
        $user = User::factory()->unverified()->create([
            'email' => '<EMAIL>',
        ]);

        // Test that user can trigger verification email
        $this->assertTrue(method_exists($user, 'sendEmailVerificationNotification'));
        
        // Test that user can trigger password reset
        $this->assertTrue(method_exists($user, 'sendPasswordResetNotification'));
        
        // Test that the user implements the required interfaces
        $this->assertInstanceOf(\Illuminate\Contracts\Auth\MustVerifyEmail::class, $user);
        $this->assertInstanceOf(\Illuminate\Contracts\Auth\CanResetPassword::class, $user);
        
        // Test that Laravel's default notifications exist
        $this->assertTrue(class_exists(\Illuminate\Auth\Notifications\VerifyEmail::class));
        $this->assertTrue(class_exists(\Illuminate\Auth\Notifications\ResetPassword::class));
    }

    /**
     * Test email provider status checking.
     */
    public function test_email_provider_status_checking()
    {
        $providers = ['smtp', 'sendgrid', 'log'];
        
        foreach ($providers as $provider) {
            Config::set('mail.default', $provider);
            
            // Test basic configuration
            $this->assertEquals($provider, config('mail.default'));
            
            // Test from address and name
            $this->assertNotEmpty(config('mail.from.address'));
            $this->assertNotEmpty(config('mail.from.name'));
            
            // Test that EmailService can be instantiated with this provider
            $emailService = app(EmailService::class);
            $this->assertInstanceOf(EmailService::class, $emailService);
        }
    }

    /**
     * Test that email logging works correctly across providers.
     */
    public function test_email_logging_across_providers()
    {
        $providers = ['log']; // Use safe provider for testing
        
        foreach ($providers as $provider) {
            Config::set('mail.default', $provider);
            
            $emailService = app(EmailService::class);
            $testEmail = "test-{$provider}@example.com";
            $mailable = new \App\Mail\TestEmail("Test for {$provider} provider");
            
            $initialCount = EmailLog::count();
            
            // Send email
            $result = $emailService->send($mailable, $testEmail, [
                'provider_test' => $provider,
            ]);
            
            // Verify
            $this->assertTrue($result, "Email should send successfully via {$provider}");
            $this->assertGreaterThan($initialCount, EmailLog::count(), 
                "Email should be logged for {$provider} provider");
            
            // Check log details
            $log = EmailLog::where('to_email', $testEmail)->first();
            $this->assertNotNull($log);
            $this->assertEquals($provider, $log->provider);
        }
    }

    /**
     * Test that the system handles email sending errors gracefully.
     */
    public function test_email_error_handling()
    {
        // Test with invalid configuration
        Config::set('mail.default', 'smtp');
        Config::set('mail.mailers.smtp.host', 'invalid-host-that-does-not-exist.com');
        Config::set('mail.mailers.smtp.port', 99999);
        
        $emailService = app(EmailService::class);
        $mailable = new \App\Mail\TestEmail('Error handling test');
        
        // This should handle the error gracefully and return false
        $result = $emailService->send($mailable, '<EMAIL>');
        
        // The service should handle errors gracefully
        $this->assertIsBool($result, 'Email service should return boolean result');
        
        // Reset to safe configuration
        Config::set('mail.default', 'log');
    }
}
