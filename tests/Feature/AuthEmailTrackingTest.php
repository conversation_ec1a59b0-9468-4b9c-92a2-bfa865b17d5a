<?php

namespace Tests\Feature;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class AuthEmailTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    /**
     * Test that verification emails are tracked via events without custom notifications.
     */
    public function test_verification_emails_are_tracked_via_events()
    {
        // Create a user
        $user = User::factory()->unverified()->create();

        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();

        // Trigger verification email
        event(new Registered($user));

        // Assert that <PERSON><PERSON>'s default VerifyEmail notification was used
        Notification::assertSentTo($user, VerifyEmail::class);

        // Assert that the email was logged via the event listener
        $this->assertDatabaseHas('email_logs', [
            'to_email' => $user->email,
            'status' => 'sent',
        ]);

        // Get the email log
        $emailLog = EmailLog::where('to_email', $user->email)->first();
        
        // Assert that a sent event was created
        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
        ]);
    }

    /**
     * Test that password reset emails are tracked via events without custom notifications.
     */
    public function test_password_reset_emails_are_tracked_via_events()
    {
        // Create a user
        $user = User::factory()->create();

        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();

        // Trigger password reset
        Password::sendResetLink(['email' => $user->email]);

        // Assert that Laravel's default ResetPassword notification was used
        Notification::assertSentTo($user, ResetPassword::class);

        // Assert that the email was logged via the event listener
        $this->assertDatabaseHas('email_logs', [
            'to_email' => $user->email,
            'status' => 'sent',
        ]);

        // Get the email log
        $emailLog = EmailLog::where('to_email', $user->email)->first();
        
        // Assert that a sent event was created
        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
        ]);
    }

    /**
     * Test that tracking is injected into authentication emails.
     */
    public function test_tracking_is_injected_into_auth_emails()
    {
        // Mock the EmailTrackingService to verify tracking injection
        $this->mock(\App\Services\EmailTrackingService::class, function ($mock) {
            $mock->shouldReceive('generateTrackingPixel')
                ->andReturn('<img src="tracking-pixel-url" />');
            
            $mock->shouldReceive('wrapLinksWithTracking')
                ->andReturnUsing(function ($content) {
                    return $content . '<!-- Links tracked -->';
                });
        });

        // Create a user
        $user = User::factory()->unverified()->create();

        // Enable real mail to check content
        Mail::fake();

        // Trigger verification email
        event(new Registered($user));

        // Assert notification was sent
        Notification::assertSentTo($user, VerifyEmail::class);

        // Assert that the email was logged
        $this->assertDatabaseHas('email_logs', [
            'to_email' => $user->email,
        ]);
    }

    /**
     * Test that business emails still use EmailService directly.
     */
    public function test_business_emails_use_email_service_directly()
    {
        // Create a user
        $user = User::factory()->create();

        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();

        // Create a test mailable
        $mailable = new \App\Mail\TestEmail();

        // Send via EmailService
        $emailService = app(\App\Services\EmailService::class);
        $result = $emailService->send($mailable, $user->email, [
            'user_id' => $user->id,
        ]);

        // Assert email was sent successfully
        $this->assertTrue($result);

        // Assert that the email was logged
        $this->assertDatabaseHas('email_logs', [
            'to_email' => $user->email,
        ]);

        // Get the email log
        $emailLog = EmailLog::where('to_email', $user->email)->first();
        
        // Assert that a sent event was created
        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
        ]);
    }
}
