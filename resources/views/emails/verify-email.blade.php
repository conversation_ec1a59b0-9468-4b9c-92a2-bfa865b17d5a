<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $mailMessage->subject ?? 'Verify Email Address' }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #1d4ed8;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .intro-line, .outro-line {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{ $appName }}</div>
        </div>

        <div class="content">
            <h2>Email Verification Required</h2>
            
            @if($user)
                <p>Hello {{ $user->name ?? 'there' }},</p>
            @endif

            @foreach($introLines as $line)
                <p class="intro-line">{{ $line }}</p>
            @endforeach

            @if($actionUrl && $actionText)
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ $actionUrl }}" class="button">{{ $actionText }}</a>
                </div>
            @endif

            @foreach($outroLines as $line)
                <p class="outro-line">{{ $line }}</p>
            @endforeach

            @if($actionUrl)
                <p style="font-size: 14px; color: #6b7280; margin-top: 30px;">
                    If you're having trouble clicking the button, copy and paste the URL below into your web browser:
                    <br>
                    <a href="{{ $actionUrl }}" style="color: #2563eb; word-break: break-all;">{{ $actionUrl }}</a>
                </p>
            @endif
        </div>

        <div class="footer">
            <p>This email was sent from {{ $appName }}.</p>
            <p>If you did not request this verification, please ignore this email.</p>
        </div>
    </div>
</body>
</html>
