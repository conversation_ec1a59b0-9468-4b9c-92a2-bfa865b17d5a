<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $mailMessage->subject ?? 'Reset Password Notification' }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #b91c1c;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .intro-line, .outro-line {
            margin-bottom: 15px;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{ $appName }}</div>
        </div>

        <div class="content">
            <h2>Password Reset Request</h2>
            
            @if($user)
                <p>Hello {{ $user->name ?? 'there' }},</p>
            @endif

            @foreach($introLines as $line)
                <p class="intro-line">{{ $line }}</p>
            @endforeach

            @if($actionUrl && $actionText)
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ $actionUrl }}" class="button">{{ $actionText }}</a>
                </div>
            @endif

            @foreach($outroLines as $line)
                <p class="outro-line">{{ $line }}</p>
            @endforeach

            <div class="warning">
                <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email and consider changing your password as a precaution.
            </div>

            @if($actionUrl)
                <p style="font-size: 14px; color: #6b7280; margin-top: 30px;">
                    If you're having trouble clicking the button, copy and paste the URL below into your web browser:
                    <br>
                    <a href="{{ $actionUrl }}" style="color: #dc2626; word-break: break-all;">{{ $actionUrl }}</a>
                </p>
            @endif
        </div>

        <div class="footer">
            <p>This email was sent from {{ $appName }}.</p>
            <p>For security reasons, this link will expire in {{ config('auth.passwords.users.expire', 60) }} minutes.</p>
        </div>
    </div>
</body>
</html>
