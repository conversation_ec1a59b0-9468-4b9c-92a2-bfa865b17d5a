{{ $appName }}

Password Reset Request

@if($user)
Hello {{ $user->name ?? 'there' }},
@endif

@foreach($introLines as $line)
{{ $line }}

@endforeach

@if($actionUrl && $actionText)
{{ $actionText }}: {{ $actionUrl }}
@endif

@foreach($outroLines as $line)
{{ $line }}

@endforeach

SECURITY NOTICE: If you did not request this password reset, please ignore this email and consider changing your password as a precaution.

---
This email was sent from {{ $appName }}.
For security reasons, this link will expire in {{ config('auth.passwords.users.expire', 60) }} minutes.
