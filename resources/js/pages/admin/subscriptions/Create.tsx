import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
    ArrowLeft, 
    Save, 
    CreditCard,
    User,
    Calendar,
    DollarSign,
    Settings
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { toast } from 'sonner';
import { PricingPlan, User as UserType } from '@/types';

type SubscriptionFormData = {
    user_id: string;
    pricing_plan_id: string;
    status: string;
    start_date: string;
    end_date: string;
    payment_gateway: string;
    paddle_subscription_id: string;
    shurjopay_subscription_id: string;
    coinbase_commerce_subscription_id: string;
    notes: string;
} & Record<string, any>;

interface Props {
    users: UserType[];
    pricingPlans: PricingPlan[];
}

export default function CreateSubscription({ users, pricingPlans }: Props) {
    const { data, setData, post, processing, errors } = useForm<SubscriptionFormData>({
        user_id: '',
        pricing_plan_id: '',
        status: 'active',
        start_date: new Date().toISOString().split('T')[0],
        end_date: '',
        payment_gateway: 'offline',
        paddle_subscription_id: '',
        shurjopay_subscription_id: '',
        coinbase_commerce_subscription_id: '',
        notes: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.subscriptions.store'), {
            onSuccess: () => {
                toast.success('Subscription created successfully.');
            },
            onError: () => {
                toast.error('Failed to create subscription. Please check the form and try again.');
            }
        });
    };

    const selectedPlan = pricingPlans.find(plan => plan.id.toString() === data.pricing_plan_id);

    const handleStartDateChange = (value: string) => {
        setData('start_date', value);
        if (selectedPlan) {
            const startDate = new Date(value);
            const endDate = new Date(startDate);
            
            if (selectedPlan.interval === 'month') {
                endDate.setMonth(endDate.getMonth() + 1);
            } else if (selectedPlan.interval === 'year') {
                endDate.setFullYear(endDate.getFullYear() + 1);
            }
            
            setData('end_date', endDate.toISOString().split('T')[0]);
        }
    };

    const handlePlanChange = (value: string) => {
        setData('pricing_plan_id', value);
        const plan = pricingPlans.find(p => p.id.toString() === value);
        if (plan && data.start_date) {
            const startDate = new Date(data.start_date);
            const endDate = new Date(startDate);
            
            if (plan.interval === 'month') {
                endDate.setMonth(endDate.getMonth() + 1);
            } else if (plan.interval === 'year') {
                endDate.setFullYear(endDate.getFullYear() + 1);
            }
            
            setData('end_date', endDate.toISOString().split('T')[0]);
        }
    };

    return (
        <AppLayout>
            <Head title="Create Subscription - Admin" />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.subscriptions.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Subscriptions
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                    Create New Subscription
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    Create a new subscription for a user
                                </p>
                            </div>
                        </div>
                        <CreditCard className="h-8 w-8 text-muted-foreground" />
                    </div>

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid gap-6 lg:grid-cols-2">
                            {/* User & Plan Selection */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        User & Plan Selection
                                    </CardTitle>
                                    <CardDescription>
                                        Select the user and pricing plan for this subscription
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="user_id">User *</Label>
                                        <Select value={data.user_id} onValueChange={(value) => setData('user_id', value)}>
                                            <SelectTrigger className={errors.user_id ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a user" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {users.map((user) => (
                                                    <SelectItem key={user.id} value={user.id.toString()}>
                                                        {user.name} ({user.email})
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.user_id && (
                                            <p className="text-sm text-red-500 mt-1">{errors.user_id}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="pricing_plan_id">Pricing Plan *</Label>
                                        <Select value={data.pricing_plan_id} onValueChange={handlePlanChange}>
                                            <SelectTrigger className={errors.pricing_plan_id ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a pricing plan" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {pricingPlans.map((plan) => (
                                                    <SelectItem key={plan.id} value={plan.id.toString()}>
                                                        {plan.display_name} - {plan.formatted_price}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.pricing_plan_id && (
                                            <p className="text-sm text-red-500 mt-1">{errors.pricing_plan_id}</p>
                                        )}
                                        {selectedPlan && (
                                            <div className="mt-2 p-3 bg-muted rounded-md">
                                                <p className="text-sm font-medium">{selectedPlan.display_name}</p>
                                                <p className="text-xs text-muted-foreground">{selectedPlan.description}</p>
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Search Limit: {selectedPlan.search_limit === -1 ? 'Unlimited' : selectedPlan.search_limit}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Subscription Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Subscription Settings
                                    </CardTitle>
                                    <CardDescription>
                                        Configure subscription status and payment gateway
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="status">Status *</Label>
                                        <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                            <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="cancelled">Cancelled</SelectItem>
                                                <SelectItem value="expired">Expired</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && (
                                            <p className="text-sm text-red-500 mt-1">{errors.status}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="payment_gateway">Payment Gateway *</Label>
                                        <Select value={data.payment_gateway} onValueChange={(value) => setData('payment_gateway', value)}>
                                            <SelectTrigger className={errors.payment_gateway ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select payment gateway" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="offline">Offline Payment</SelectItem>
                                                <SelectItem value="paddle">Paddle</SelectItem>
                                                <SelectItem value="shurjopay">ShurjoPay</SelectItem>
                                                <SelectItem value="coinbase_commerce">Coinbase Commerce</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.payment_gateway && (
                                            <p className="text-sm text-red-500 mt-1">{errors.payment_gateway}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Select the payment method used for this subscription
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Date Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Date Configuration
                                </CardTitle>
                                <CardDescription>
                                    Set the subscription period dates
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="start_date">Start Date *</Label>
                                        <Input
                                            id="start_date"
                                            type="date"
                                            value={data.start_date}
                                            onChange={(e) => handleStartDateChange(e.target.value)}
                                            className={errors.start_date ? 'border-red-500' : ''}
                                        />
                                        {errors.start_date && (
                                            <p className="text-sm text-red-500 mt-1">{errors.start_date}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="end_date">End Date</Label>
                                        <Input
                                            id="end_date"
                                            type="date"
                                            value={data.end_date}
                                            onChange={(e) => setData('end_date', e.target.value)}
                                            className={errors.end_date ? 'border-red-500' : ''}
                                        />
                                        {errors.end_date && (
                                            <p className="text-sm text-red-500 mt-1">{errors.end_date}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Leave empty for auto-calculation based on plan interval
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Payment Gateway IDs */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5" />
                                    Payment Gateway IDs
                                </CardTitle>
                                <CardDescription>
                                    Optional external subscription IDs from payment gateways
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-3">
                                    <div>
                                        <Label htmlFor="paddle_subscription_id">Paddle Subscription ID</Label>
                                        <Input
                                            id="paddle_subscription_id"
                                            value={data.paddle_subscription_id}
                                            onChange={(e) => setData('paddle_subscription_id', e.target.value)}
                                            placeholder="sub_01234567890"
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="shurjopay_subscription_id">ShurjoPay Subscription ID</Label>
                                        <Input
                                            id="shurjopay_subscription_id"
                                            value={data.shurjopay_subscription_id}
                                            onChange={(e) => setData('shurjopay_subscription_id', e.target.value)}
                                            placeholder="sp_01234567890"
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="coinbase_commerce_subscription_id">Coinbase Commerce ID</Label>
                                        <Input
                                            id="coinbase_commerce_subscription_id"
                                            value={data.coinbase_commerce_subscription_id}
                                            onChange={(e) => setData('coinbase_commerce_subscription_id', e.target.value)}
                                            placeholder="cb_01234567890"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Notes */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Additional Notes</CardTitle>
                                <CardDescription>
                                    Optional notes about this subscription
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div>
                                    <Label htmlFor="notes">Notes</Label>
                                    <Textarea
                                        id="notes"
                                        value={data.notes}
                                        onChange={(e) => setData('notes', e.target.value)}
                                        placeholder="Enter any additional notes about this subscription..."
                                        rows={3}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Submit Button */}
                        <div className="flex justify-end gap-4">
                            <Link href={route('admin.subscriptions.index')}>
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="h-4 w-4 mr-2" />
                                {processing ? 'Creating...' : 'Create Subscription'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
