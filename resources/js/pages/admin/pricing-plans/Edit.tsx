import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { toast } from 'sonner';
import { useState, useEffect, useCallback } from 'react';

interface PlanMetadata {
    [key: string]: string | number | boolean | null;
}

interface PricingPlan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    metadata: PlanMetadata;

    // Payment method controls
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    crypto_payment_enabled: boolean;

    // Integration fields
    paddle_product_id: string;
    paddle_price_id_monthly: string;
    paddle_price_id_yearly: string;
    shurjopay_product_id: string;
    shurjopay_price_id_monthly: string;
    shurjopay_price_id_yearly: string;
    coinbase_commerce_product_id: string;
    coinbase_commerce_price_id_monthly: string;
    coinbase_commerce_price_id_yearly: string;

    // Fee configuration
    paddle_fee_percentage: number;
    paddle_fee_fixed: number;
    shurjopay_fee_percentage: number;
    shurjopay_fee_fixed: number;
    coinbase_commerce_fee_percentage: number;
    coinbase_commerce_fee_fixed: number;
    offline_fee_percentage: number;
    offline_fee_fixed: number;
    fee_handling: string;
    show_fees_breakdown: boolean;
    tax_percentage: number;
    tax_inclusive: boolean;
}

interface Props {
    pricingPlan: PricingPlan;
}

export default function Edit({ pricingPlan }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: pricingPlan.name,
        display_name: pricingPlan.display_name,
        description: pricingPlan.description || '',
        price: pricingPlan.price,
        currency: pricingPlan.currency,
        interval: pricingPlan.interval,
        features: pricingPlan.features || [''],
        search_limit: pricingPlan.search_limit,
        is_active: pricingPlan.is_active,
        is_default: pricingPlan.is_default,
        is_popular: pricingPlan.is_popular,
        sort_order: pricingPlan.sort_order,
        metadata: pricingPlan.metadata || {},

        // Payment method controls
        online_payment_enabled: pricingPlan.online_payment_enabled,
        offline_payment_enabled: pricingPlan.offline_payment_enabled,
        crypto_payment_enabled: pricingPlan.crypto_payment_enabled || false,

        // Integration fields
        paddle_product_id: pricingPlan.paddle_product_id || '',
        paddle_price_id_monthly: pricingPlan.paddle_price_id_monthly || '',
        paddle_price_id_yearly: pricingPlan.paddle_price_id_yearly || '',
        shurjopay_product_id: pricingPlan.shurjopay_product_id || '',
        shurjopay_price_id_monthly: pricingPlan.shurjopay_price_id_monthly || '',
        shurjopay_price_id_yearly: pricingPlan.shurjopay_price_id_yearly || '',
        coinbase_commerce_product_id: pricingPlan.coinbase_commerce_product_id || '',
        coinbase_commerce_price_id_monthly: pricingPlan.coinbase_commerce_price_id_monthly || '',
        coinbase_commerce_price_id_yearly: pricingPlan.coinbase_commerce_price_id_yearly || '',

        // Fee configuration
        paddle_fee_percentage: pricingPlan.paddle_fee_percentage || 0,
        paddle_fee_fixed: pricingPlan.paddle_fee_fixed || 0,
        shurjopay_fee_percentage: pricingPlan.shurjopay_fee_percentage || 0,
        shurjopay_fee_fixed: pricingPlan.shurjopay_fee_fixed || 0,
        coinbase_commerce_fee_percentage: pricingPlan.coinbase_commerce_fee_percentage || 0,
        coinbase_commerce_fee_fixed: pricingPlan.coinbase_commerce_fee_fixed || 0,
        offline_fee_percentage: pricingPlan.offline_fee_percentage || 0,
        offline_fee_fixed: pricingPlan.offline_fee_fixed || 0,
        fee_handling: pricingPlan.fee_handling || 'absorb',
        show_fees_breakdown: pricingPlan.show_fees_breakdown || false,
        tax_percentage: pricingPlan.tax_percentage || 0,
        tax_inclusive: pricingPlan.tax_inclusive || false,
    });

    const [features, setFeatures] = useState(pricingPlan.features || ['']);

    // Memoize the setData function to avoid unnecessary re-renders
    const updateFeatures = useCallback((newFeatures: string[]) => {
        setData('features', newFeatures);
    }, [setData]);

    useEffect(() => {
        updateFeatures(features);
    }, [features, updateFeatures]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Client-side validation
        if (!data.name.trim()) {
            toast.error('Plan name is required');
            return;
        }
        if (!data.display_name.trim()) {
            toast.error('Display name is required');
            return;
        }
        if (data.features.filter(f => f.trim()).length === 0) {
            toast.error('At least one feature is required');
            return;
        }

        put(route('admin.pricing-plans.update', pricingPlan.id), {
            onSuccess: () => {
                toast.success('Pricing plan updated successfully!');
            },
            onError: () => {
                toast.error('Failed to update pricing plan. Please check the form for errors.');
            },
        });
    };

    const addFeature = () => {
        const newFeatures = [...features, ''];
        setFeatures(newFeatures);
    };

    const removeFeature = (index: number) => {
        const newFeatures = features.filter((_, i) => i !== index);
        setFeatures(newFeatures);
    };

    const updateFeature = (index: number, value: string) => {
        const newFeatures = [...features];
        newFeatures[index] = value;
        setFeatures(newFeatures);
    };

    return (
        <AppLayout>
            <Head title={`Edit ${pricingPlan.display_name}`} />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href={route('admin.pricing-plans.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Plans
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Edit {pricingPlan.display_name}</h1>
                        <p className="text-muted-foreground mt-2">
                            Update the pricing plan details
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Tabs defaultValue="basic" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="basic">Basic Info</TabsTrigger>
                            <TabsTrigger value="payment">Payment Methods</TabsTrigger>
                            <TabsTrigger value="integrations">Integrations</TabsTrigger>
                            <TabsTrigger value="advanced">Advanced</TabsTrigger>
                        </TabsList>

                        {/* Basic Info Tab */}
                        <TabsContent value="basic" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Information</CardTitle>
                                <CardDescription>
                                    Configure the basic details of the pricing plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Plan Name (Internal)</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., premium, enterprise"
                                        required
                                    />
                                    {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="display_name">Display Name</Label>
                                    <Input
                                        id="display_name"
                                        value={data.display_name}
                                        onChange={(e) => setData('display_name', e.target.value)}
                                        placeholder="e.g., Premium Plan"
                                        required
                                    />
                                    {errors.display_name && <p className="text-sm text-destructive">{errors.display_name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Brief description of the plan"
                                        rows={3}
                                    />
                                    {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Pricing */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Pricing</CardTitle>
                                <CardDescription>
                                    Set the price and billing interval
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="price">Price</Label>
                                        <Input
                                            id="price"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={data.price}
                                            onChange={(e) => setData('price', parseFloat(e.target.value) || 0)}
                                            required
                                        />
                                        {errors.price && <p className="text-sm text-destructive">{errors.price}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="currency">Currency</Label>
                                        <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="USD">USD</SelectItem>
                                                <SelectItem value="EUR">EUR</SelectItem>
                                                <SelectItem value="GBP">GBP</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.currency && <p className="text-sm text-destructive">{errors.currency}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="interval">Billing Interval</Label>
                                    <Select value={data.interval} onValueChange={(value) => setData('interval', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Monthly</SelectItem>
                                            <SelectItem value="year">Yearly</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.interval && <p className="text-sm text-destructive">{errors.interval}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="search_limit">Search Limit (per day)</Label>
                                    <Input
                                        id="search_limit"
                                        type="number"
                                        min="-1"
                                        value={data.search_limit}
                                        onChange={(e) => setData('search_limit', parseInt(e.target.value) || 0)}
                                        placeholder="-1 for unlimited"
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">Use -1 for unlimited searches</p>
                                    {errors.search_limit && <p className="text-sm text-destructive">{errors.search_limit}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        min="0"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        required
                                    />
                                    {errors.sort_order && <p className="text-sm text-destructive">{errors.sort_order}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Features */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Features</CardTitle>
                                <CardDescription>
                                    List the features included in this plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {features.map((feature, index) => (
                                    <div key={index} className="flex gap-2">
                                        <Input
                                            value={feature}
                                            onChange={(e) => updateFeature(index, e.target.value)}
                                            placeholder="Enter feature description"
                                        />
                                        {features.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeFeature(index)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addFeature}
                                >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Feature
                                </Button>
                                {errors.features && <p className="text-sm text-destructive">{errors.features}</p>}
                            </CardContent>
                        </Card>

                        {/* Settings */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Settings</CardTitle>
                                <CardDescription>
                                    Configure plan visibility and behavior
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Active</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Make this plan available for subscription
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Default Plan</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Assign to new users by default
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_default}
                                        onCheckedChange={(checked) => setData('is_default', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Popular Plan</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Highlight this plan as popular
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_popular}
                                        onCheckedChange={(checked) => setData('is_popular', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                            </div>
                        </TabsContent>

                        {/* Payment Methods Tab */}
                        <TabsContent value="payment" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                                {/* Payment Methods */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Payment Methods</CardTitle>
                                        <CardDescription>
                                            Configure available payment options for this plan
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Online Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow credit card payments via Paddle
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.online_payment_enabled}
                                                onCheckedChange={(checked) => setData('online_payment_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Offline Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow manual payment requests and verification
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.offline_payment_enabled}
                                                onCheckedChange={(checked) => setData('offline_payment_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Crypto Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow cryptocurrency payments via Coinbase Commerce
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.crypto_payment_enabled}
                                                onCheckedChange={(checked) => setData('crypto_payment_enabled', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Settings */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Settings</CardTitle>
                                        <CardDescription>
                                            Configure plan visibility and behavior
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Active</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Make this plan available for subscription
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_active}
                                                onCheckedChange={(checked) => setData('is_active', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Default Plan</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Assign to new users by default
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_default}
                                                onCheckedChange={(checked) => setData('is_default', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Popular Plan</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Highlight this plan as popular
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_popular}
                                                onCheckedChange={(checked) => setData('is_popular', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        {/* Integrations Tab */}
                        <TabsContent value="integrations" className="space-y-6">
                            <div className="grid gap-6">
                                {/* Paddle Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Paddle Integration</CardTitle>
                                        <CardDescription>
                                            Configure Paddle payment gateway settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_product_id">Paddle Product ID</Label>
                                                <Input
                                                    id="paddle_product_id"
                                                    value={data.paddle_product_id}
                                                    onChange={(e) => setData('paddle_product_id', e.target.value)}
                                                    placeholder="Product ID from your Paddle dashboard"
                                                    className={errors.paddle_product_id ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Product ID from your Paddle dashboard
                                                </p>
                                                {errors.paddle_product_id && <p className="text-sm text-red-500 mt-1">{errors.paddle_product_id}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_price_id_monthly">Monthly Price ID</Label>
                                                <Input
                                                    id="paddle_price_id_monthly"
                                                    value={data.paddle_price_id_monthly}
                                                    onChange={(e) => setData('paddle_price_id_monthly', e.target.value)}
                                                    placeholder="Monthly billing price ID from Paddle"
                                                    className={errors.paddle_price_id_monthly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Monthly billing price ID from Paddle
                                                </p>
                                                {errors.paddle_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.paddle_price_id_monthly}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_price_id_yearly">Yearly Price ID</Label>
                                                <Input
                                                    id="paddle_price_id_yearly"
                                                    value={data.paddle_price_id_yearly}
                                                    onChange={(e) => setData('paddle_price_id_yearly', e.target.value)}
                                                    placeholder="Yearly billing price ID from Paddle"
                                                    className={errors.paddle_price_id_yearly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Yearly billing price ID from Paddle
                                                </p>
                                                {errors.paddle_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.paddle_price_id_yearly}</p>}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* ShurjoPay Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>ShurjoPay Integration</CardTitle>
                                        <CardDescription>
                                            Configure ShurjoPay payment gateway settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_product_id">ShurjoPay Product ID</Label>
                                                <Input
                                                    id="shurjopay_product_id"
                                                    value={data.shurjopay_product_id}
                                                    onChange={(e) => setData('shurjopay_product_id', e.target.value)}
                                                    placeholder="Product ID from your ShurjoPay dashboard"
                                                    className={errors.shurjopay_product_id ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Product ID from your ShurjoPay dashboard
                                                </p>
                                                {errors.shurjopay_product_id && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_product_id}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_price_id_monthly">Monthly Price ID</Label>
                                                <Input
                                                    id="shurjopay_price_id_monthly"
                                                    value={data.shurjopay_price_id_monthly}
                                                    onChange={(e) => setData('shurjopay_price_id_monthly', e.target.value)}
                                                    placeholder="Monthly billing price ID from ShurjoPay"
                                                    className={errors.shurjopay_price_id_monthly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Monthly billing price ID from ShurjoPay
                                                </p>
                                                {errors.shurjopay_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_price_id_monthly}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_price_id_yearly">Yearly Price ID</Label>
                                                <Input
                                                    id="shurjopay_price_id_yearly"
                                                    value={data.shurjopay_price_id_yearly}
                                                    onChange={(e) => setData('shurjopay_price_id_yearly', e.target.value)}
                                                    placeholder="Yearly billing price ID from ShurjoPay"
                                                    className={errors.shurjopay_price_id_yearly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Yearly billing price ID from ShurjoPay
                                                </p>
                                                {errors.shurjopay_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_price_id_yearly}</p>}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Coinbase Commerce Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Coinbase Commerce Integration</CardTitle>
                                        <CardDescription>
                                            Configure Coinbase Commerce cryptocurrency payment settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_product_id">Coinbase Commerce Product ID</Label>
                                                <Input
                                                    id="coinbase_commerce_product_id"
                                                    value={data.coinbase_commerce_product_id}
                                                    onChange={(e) => setData('coinbase_commerce_product_id', e.target.value)}
                                                    placeholder="Product ID from your Coinbase Commerce dashboard"
                                                    className={errors.coinbase_commerce_product_id ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Product ID from your Coinbase Commerce dashboard
                                                </p>
                                                {errors.coinbase_commerce_product_id && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_product_id}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_price_id_monthly">Monthly Price ID</Label>
                                                <Input
                                                    id="coinbase_commerce_price_id_monthly"
                                                    value={data.coinbase_commerce_price_id_monthly}
                                                    onChange={(e) => setData('coinbase_commerce_price_id_monthly', e.target.value)}
                                                    placeholder="Monthly billing price ID from Coinbase Commerce"
                                                    className={errors.coinbase_commerce_price_id_monthly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Monthly billing price ID from Coinbase Commerce
                                                </p>
                                                {errors.coinbase_commerce_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_price_id_monthly}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_price_id_yearly">Yearly Price ID</Label>
                                                <Input
                                                    id="coinbase_commerce_price_id_yearly"
                                                    value={data.coinbase_commerce_price_id_yearly}
                                                    onChange={(e) => setData('coinbase_commerce_price_id_yearly', e.target.value)}
                                                    placeholder="Yearly billing price ID from Coinbase Commerce"
                                                    className={errors.coinbase_commerce_price_id_yearly ? 'border-red-500' : ''}
                                                />
                                                <p className="text-xs text-muted-foreground">
                                                    Yearly billing price ID from Coinbase Commerce
                                                </p>
                                                {errors.coinbase_commerce_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_price_id_yearly}</p>}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        {/* Advanced Tab */}
                        <TabsContent value="advanced" className="space-y-6">
                            <div className="grid gap-6">
                                {/* Fee Configuration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Fee Configuration</CardTitle>
                                        <CardDescription>
                                            Configure payment processing fees and handling
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="fee_handling">Fee Handling</Label>
                                            <Select value={data.fee_handling} onValueChange={(value) => setData('fee_handling', value)}>
                                                <SelectTrigger className={errors.fee_handling ? 'border-red-500' : ''}>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="absorb">Absorb fees (included in price)</SelectItem>
                                                    <SelectItem value="pass_to_customer">Pass to customer (added to price)</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.fee_handling && <p className="text-sm text-red-500 mt-1">{errors.fee_handling}</p>}
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Show Fees Breakdown</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Display fee breakdown to customers
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.show_fees_breakdown}
                                                onCheckedChange={(checked) => setData('show_fees_breakdown', checked)}
                                            />
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_fee_percentage">Paddle Fee %</Label>
                                                <Input
                                                    id="paddle_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.paddle_fee_percentage}
                                                    onChange={(e) => setData('paddle_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.paddle_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.paddle_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.paddle_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_fee_fixed">Paddle Fee Fixed</Label>
                                                <Input
                                                    id="paddle_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.paddle_fee_fixed}
                                                    onChange={(e) => setData('paddle_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.paddle_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.paddle_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.paddle_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_fee_percentage">ShurjoPay Fee %</Label>
                                                <Input
                                                    id="shurjopay_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.shurjopay_fee_percentage}
                                                    onChange={(e) => setData('shurjopay_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.shurjopay_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.shurjopay_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_fee_fixed">ShurjoPay Fee Fixed</Label>
                                                <Input
                                                    id="shurjopay_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.shurjopay_fee_fixed}
                                                    onChange={(e) => setData('shurjopay_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.shurjopay_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.shurjopay_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_fee_percentage">Coinbase Commerce Fee %</Label>
                                                <Input
                                                    id="coinbase_commerce_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.coinbase_commerce_fee_percentage}
                                                    onChange={(e) => setData('coinbase_commerce_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.coinbase_commerce_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.coinbase_commerce_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_fee_fixed">Coinbase Commerce Fee Fixed</Label>
                                                <Input
                                                    id="coinbase_commerce_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.coinbase_commerce_fee_fixed}
                                                    onChange={(e) => setData('coinbase_commerce_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.coinbase_commerce_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.coinbase_commerce_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="offline_fee_percentage">Offline Payment Fee %</Label>
                                                <Input
                                                    id="offline_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.offline_fee_percentage}
                                                    onChange={(e) => setData('offline_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.offline_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.offline_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.offline_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="offline_fee_fixed">Offline Payment Fee Fixed</Label>
                                                <Input
                                                    id="offline_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.offline_fee_fixed}
                                                    onChange={(e) => setData('offline_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.offline_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.offline_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.offline_fee_fixed}</p>}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Tax Configuration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Tax Configuration</CardTitle>
                                        <CardDescription>
                                            Configure tax settings for this plan
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="tax_percentage">Tax Percentage</Label>
                                            <Input
                                                id="tax_percentage"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                max="100"
                                                value={data.tax_percentage}
                                                onChange={(e) => setData('tax_percentage', parseFloat(e.target.value) || 0)}
                                                className={errors.tax_percentage ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Tax percentage to apply to this plan
                                            </p>
                                            {errors.tax_percentage && <p className="text-sm text-red-500 mt-1">{errors.tax_percentage}</p>}
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Tax Inclusive</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Tax is included in the displayed price
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.tax_inclusive}
                                                onCheckedChange={(checked) => setData('tax_inclusive', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>
                    </Tabs>

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Updating...' : 'Update Plan'}
                        </Button>
                        <Link href={route('admin.pricing-plans.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
