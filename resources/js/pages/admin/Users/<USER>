import { Head, Link, router } from '@inertiajs/react';
import type { UserSearch } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
    ArrowLeft,
    User as UserIcon,
    Mail,
    Calendar,
    Activity,
    CreditCard,
    Eye,
    Ban,
    CheckCircle,
    DollarSign,
    Search,
    Heart,
    Edit,
    Save,
    X,
    Key,
    EyeOff
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import ImpersonationSecurityCheck from '@/components/ImpersonationSecurityCheck';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    status: 'active' | 'pending' | 'suspended' | 'banned';
    approval_status: 'pending' | 'approved' | 'rejected';
    subscription_plan: 'free' | 'premium';
    search_count: number;
    login_count: number;
    created_at: string;
    last_login_at: string | null;
    approved_at: string | null;
    suspended_at: string | null;
    suspension_reason: string | null;
    suspension_expires_at: string | null;
    searches_count: number;
    payment_requests_count: number;
    activity_logs_count: number;
    favorites_count: number;
    approved_by?: {
        id: number;
        name: string;
    };
    suspended_by?: {
        id: number;
        name: string;
    };
    active_subscription?: {
        id: number;
        plan_name: string;
        status: string;
        current_period_end: string;
    };
    subscriptions: Array<{
        id: number;
        plan_name: string;
        status: string;
        created_at: string;
        current_period_end: string;
    }>;
    payment_requests: Array<{
        id: number;
        amount: number;
        currency: string;
        payment_method: string;
        status: string;
        requested_at: string;
        approved_at: string | null;
    }>;
    activity_logs: Array<{
        id: number;
        activity_type: string;
        description: string;
        created_at: string;
        performed_by?: {
            name: string;
        };
    }>;
    searches: UserSearch[];
    favorites: Array<{
        id: number;
        favoritable_type: string;
        favoritable_id: number;
        favoritable?: {
            name: string;
            part_number?: string;
            model_number?: string;
        };
        created_at: string;
    }>;
}

interface Props {
    user: User;
}

const StatusBadge = ({ status }: { status: User['status'] }) => {
    const variants = {
        active: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        suspended: 'bg-red-100 text-red-800',
        banned: 'bg-gray-100 text-gray-800',
    };

    return (
        <Badge className={variants[status]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

const ApprovalBadge = ({ status }: { status: User['approval_status'] }) => {
    const variants = {
        approved: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        rejected: 'bg-red-100 text-red-800',
    };

    return (
        <Badge className={variants[status]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

export default function UserShow({ user }: Props) {
    const [isEditing, setIsEditing] = useState(false);
    const [showImpersonationDialog, setShowImpersonationDialog] = useState(false);
    const [showPasswordDialog, setShowPasswordDialog] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [editForm, setEditForm] = useState({
        name: user.name,
        email: user.email,
        subscription_plan: user.subscription_plan,
        status: user.status,
        approval_status: user.approval_status,
    });
    const [passwordForm, setPasswordForm] = useState({
        password: '',
        password_confirmation: '',
        generate_password: true,
        send_notification: true,
    });

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleSave = () => {
        router.put(`/admin/users/${user.id}`, editForm, {
            onSuccess: () => {
                setIsEditing(false);
            },
        });
    };

    const handleCancel = () => {
        setEditForm({
            name: user.name,
            email: user.email,
            subscription_plan: user.subscription_plan,
            status: user.status,
            approval_status: user.approval_status,
        });
        setIsEditing(false);
    };

    const handleApprove = () => {
        router.post(`/admin/users/${user.id}/approve`, {}, {
            preserveScroll: true,
        });
    };

    const handleSuspend = () => {
        const reason = prompt('Please provide a reason for suspension:');
        const expiresAt = prompt('Suspension expires at (YYYY-MM-DD HH:MM, leave empty for permanent):');
        
        if (reason) {
            router.post(`/admin/users/${user.id}/suspend`, {
                reason,
                expires_at: expiresAt || null,
            }, {
                preserveScroll: true,
            });
        }
    };

    const handleUnsuspend = () => {
        router.post(`/admin/users/${user.id}/unsuspend`, {}, {
            preserveScroll: true,
        });
    };

    const handleImpersonate = () => {
        setShowImpersonationDialog(true);
    };

    const handleImpersonationConfirm = (reason: string, duration: number) => {
        router.post(`/admin/impersonate/${user.id}`, {
            reason: reason,
            duration: duration,
        }, {
            onSuccess: () => {
                setShowImpersonationDialog(false);
            },
            onError: () => {
                setShowImpersonationDialog(false);
            }
        });
    };

    const handleDelete = () => {
        if (confirm(`Are you sure you want to delete user "${user.name}"? This action cannot be undone.`)) {
            router.delete(`/admin/users/${user.id}`);
        }
    };

    const handlePasswordChange = () => {
        router.post(`/admin/users/${user.id}/change-password`, passwordForm, {
            onSuccess: () => {
                setShowPasswordDialog(false);
                setPasswordForm({
                    password: '',
                    password_confirmation: '',
                    generate_password: true,
                    send_notification: true,
                });
            },
        });
    };

    const handlePasswordFormChange = (field: string, value: any) => {
        setPasswordForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    return (
        <AppLayout>
            <Head title={`User: ${user.name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/admin/users">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Users
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                    {user.name}
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    User ID: {user.id} • {user.email}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            {!isEditing ? (
                                <Button onClick={handleEdit} variant="outline">
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit User
                                </Button>
                            ) : (
                                <div className="flex gap-2">
                                    <Button onClick={handleSave}>
                                        <Save className="h-4 w-4 mr-2" />
                                        Save
                                    </Button>
                                    <Button onClick={handleCancel} variant="outline">
                                        <X className="h-4 w-4 mr-2" />
                                        Cancel
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Status Cards */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Account Status</CardTitle>
                                <UserIcon className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center gap-2">
                                    <StatusBadge status={user.status} />
                                    <ApprovalBadge status={user.approval_status} />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Searches</CardTitle>
                                <Search className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{user.searches_count}</div>
                                <p className="text-xs text-muted-foreground">
                                    Current: {user.search_count}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Logins</CardTitle>
                                <Activity className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{user.login_count}</div>
                                <p className="text-xs text-muted-foreground">
                                    {user.last_login_at ? `Last: ${new Date(user.last_login_at).toLocaleDateString()}` : 'Never'}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Subscription</CardTitle>
                                <CreditCard className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-lg font-bold text-foreground capitalize">
                                    {user.subscription_plan}
                                </div>
                                {user.active_subscription && (
                                    <p className="text-xs text-muted-foreground">
                                        Until: {new Date(user.active_subscription.current_period_end).toLocaleDateString()}
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content */}
                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* User Information */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle>User Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {isEditing ? (
                                        <div className="space-y-4">
                                            <div>
                                                <Label htmlFor="name">Name</Label>
                                                <Input
                                                    id="name"
                                                    value={editForm.name}
                                                    onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="email">Email</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={editForm.email}
                                                    onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="subscription_plan">Subscription Plan</Label>
                                                <Select value={editForm.subscription_plan} onValueChange={(value) => setEditForm({...editForm, subscription_plan: value as 'free' | 'premium'})}>
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="free">Free</SelectItem>
                                                        <SelectItem value="premium">Premium</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div>
                                                <Label htmlFor="status">Status</Label>
                                                <Select value={editForm.status} onValueChange={(value) => setEditForm({...editForm, status: value as User['status']})}>
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="active">Active</SelectItem>
                                                        <SelectItem value="pending">Pending</SelectItem>
                                                        <SelectItem value="suspended">Suspended</SelectItem>
                                                        <SelectItem value="banned">Banned</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div>
                                                <Label htmlFor="approval_status">Approval Status</Label>
                                                <Select value={editForm.approval_status} onValueChange={(value) => setEditForm({...editForm, approval_status: value as User['approval_status']})}>
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="approved">Approved</SelectItem>
                                                        <SelectItem value="pending">Pending</SelectItem>
                                                        <SelectItem value="rejected">Rejected</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2">
                                                <Mail className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">{user.email}</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">
                                                    Joined {new Date(user.created_at).toLocaleDateString()}
                                                </span>
                                            </div>
                                            {user.approved_at && (
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                    <span className="text-sm">
                                                        Approved {new Date(user.approved_at).toLocaleDateString()}
                                                        {user.approved_by && ` by ${user.approved_by.name}`}
                                                    </span>
                                                </div>
                                            )}
                                            {user.suspended_at && (
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-2">
                                                        <Ban className="h-4 w-4 text-red-600" />
                                                        <span className="text-sm">
                                                            Suspended {new Date(user.suspended_at).toLocaleDateString()}
                                                            {user.suspended_by && ` by ${user.suspended_by.name}`}
                                                        </span>
                                                    </div>
                                                    {user.suspension_reason && (
                                                        <div className="ml-6 text-sm text-muted-foreground">
                                                            Reason: {user.suspension_reason}
                                                        </div>
                                                    )}
                                                    {user.suspension_expires_at && (
                                                        <div className="ml-6 text-sm text-muted-foreground">
                                                            Expires: {new Date(user.suspension_expires_at).toLocaleDateString()}
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Quick Actions */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    {user.approval_status === 'pending' && (
                                        <Button onClick={handleApprove} className="w-full" variant="default">
                                            <CheckCircle className="h-4 w-4 mr-2" />
                                            Approve User
                                        </Button>
                                    )}
                                    {user.status === 'active' && (
                                        <Button onClick={handleSuspend} className="w-full" variant="destructive">
                                            <Ban className="h-4 w-4 mr-2" />
                                            Suspend User
                                        </Button>
                                    )}
                                    {user.status === 'suspended' && (
                                        <Button onClick={handleUnsuspend} className="w-full" variant="default">
                                            <CheckCircle className="h-4 w-4 mr-2" />
                                            Unsuspend User
                                        </Button>
                                    )}
                                    <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
                                        <DialogTrigger asChild>
                                            <Button className="w-full" variant="outline">
                                                <Key className="h-4 w-4 mr-2" />
                                                Change Password
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="sm:max-w-md">
                                            <DialogHeader>
                                                <DialogTitle>Change Password</DialogTitle>
                                                <DialogDescription>
                                                    Change the password for {user.name}
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id="generate_password"
                                                        checked={passwordForm.generate_password}
                                                        onCheckedChange={(checked) =>
                                                            handlePasswordFormChange('generate_password', checked)
                                                        }
                                                    />
                                                    <Label htmlFor="generate_password">
                                                        Generate random password
                                                    </Label>
                                                </div>

                                                {!passwordForm.generate_password && (
                                                    <div className="space-y-4">
                                                        <div>
                                                            <Label htmlFor="password">New Password</Label>
                                                            <div className="relative">
                                                                <Input
                                                                    id="password"
                                                                    type={showPassword ? 'text' : 'password'}
                                                                    value={passwordForm.password}
                                                                    onChange={(e) =>
                                                                        handlePasswordFormChange('password', e.target.value)
                                                                    }
                                                                    placeholder="Enter new password"
                                                                    className="pr-10"
                                                                />
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                                    onClick={() => setShowPassword(!showPassword)}
                                                                >
                                                                    {showPassword ? (
                                                                        <EyeOff className="h-4 w-4" />
                                                                    ) : (
                                                                        <Eye className="h-4 w-4" />
                                                                    )}
                                                                </Button>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="password_confirmation">Confirm Password</Label>
                                                            <Input
                                                                id="password_confirmation"
                                                                type={showPassword ? 'text' : 'password'}
                                                                value={passwordForm.password_confirmation}
                                                                onChange={(e) =>
                                                                    handlePasswordFormChange('password_confirmation', e.target.value)
                                                                }
                                                                placeholder="Confirm new password"
                                                            />
                                                        </div>
                                                    </div>
                                                )}

                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id="send_notification"
                                                        checked={passwordForm.send_notification}
                                                        onCheckedChange={(checked) =>
                                                            handlePasswordFormChange('send_notification', checked)
                                                        }
                                                    />
                                                    <Label htmlFor="send_notification">
                                                        Send email notification to user
                                                    </Label>
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    onClick={() => setShowPasswordDialog(false)}
                                                >
                                                    Cancel
                                                </Button>
                                                <Button onClick={handlePasswordChange}>
                                                    Change Password
                                                </Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>
                                    <Button onClick={handleImpersonate} className="w-full" variant="outline">
                                        <Eye className="h-4 w-4 mr-2" />
                                        Login as User
                                    </Button>
                                    <Button onClick={handleDelete} className="w-full" variant="destructive">
                                        <X className="h-4 w-4 mr-2" />
                                        Delete User
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Detailed Information Tabs */}
                        <div className="lg:col-span-2">
                            <Tabs defaultValue="activity" className="space-y-4">
                                <TabsList className="grid w-full grid-cols-4">
                                    <TabsTrigger value="activity">Activity</TabsTrigger>
                                    <TabsTrigger value="payments">Payments</TabsTrigger>
                                    <TabsTrigger value="searches">Searches</TabsTrigger>
                                    <TabsTrigger value="favorites">Favorites</TabsTrigger>
                                </TabsList>

                                {/* Activity Tab */}
                                <TabsContent value="activity">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Recent Activity</CardTitle>
                                            <CardDescription>
                                                Latest {user.activity_logs.length} activities
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-4">
                                                {user.activity_logs.length > 0 ? (
                                                    user.activity_logs.map((log) => (
                                                        <div key={log.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                                            <Activity className="h-4 w-4 mt-1 text-blue-600" />
                                                            <div className="flex-1 space-y-1">
                                                                <div className="flex items-center justify-between">
                                                                    <p className="text-sm font-medium">
                                                                        {log.activity_type.replace('_', ' ').toUpperCase()}
                                                                    </p>
                                                                    <span className="text-xs text-muted-foreground">
                                                                        {new Date(log.created_at).toLocaleString()}
                                                                    </span>
                                                                </div>
                                                                <p className="text-sm text-muted-foreground">
                                                                    {log.description}
                                                                </p>
                                                                {log.performed_by && (
                                                                    <p className="text-xs text-muted-foreground">
                                                                        by {log.performed_by.name}
                                                                    </p>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <p className="text-center text-muted-foreground py-8">
                                                        No activity logs found
                                                    </p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                {/* Payments Tab */}
                                <TabsContent value="payments">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Payment Requests</CardTitle>
                                            <CardDescription>
                                                {user.payment_requests.length} payment requests
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-4">
                                                {user.payment_requests.length > 0 ? (
                                                    user.payment_requests.map((payment) => (
                                                        <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                            <div className="flex items-center gap-3">
                                                                <DollarSign className="h-4 w-4 text-green-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium">
                                                                        {payment.currency} {payment.amount}
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        {payment.payment_method} • {new Date(payment.requested_at).toLocaleDateString()}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <Badge className={
                                                                payment.status === 'approved' ? 'bg-green-100 text-green-800' :
                                                                payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                                payment.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                                                'bg-blue-100 text-blue-800'
                                                            }>
                                                                {payment.status}
                                                            </Badge>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <p className="text-center text-muted-foreground py-8">
                                                        No payment requests found
                                                    </p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Subscription History */}
                                    <Card className="mt-4">
                                        <CardHeader>
                                            <CardTitle>Subscription History</CardTitle>
                                            <CardDescription>
                                                {user.subscriptions.length} subscriptions
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-4">
                                                {user.subscriptions.length > 0 ? (
                                                    user.subscriptions.map((subscription) => (
                                                        <div key={subscription.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                            <div className="flex items-center gap-3">
                                                                <CreditCard className="h-4 w-4 text-purple-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium capitalize">
                                                                        {subscription.plan_name}
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        Started: {new Date(subscription.created_at).toLocaleDateString()}
                                                                        {subscription.current_period_end && (
                                                                            ` • Ends: ${new Date(subscription.current_period_end).toLocaleDateString()}`
                                                                        )}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <Badge className={
                                                                subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                                                                subscription.status === 'canceled' ? 'bg-red-100 text-red-800' :
                                                                'bg-gray-100 text-gray-800'
                                                            }>
                                                                {subscription.status}
                                                            </Badge>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <p className="text-center text-muted-foreground py-8">
                                                        No subscription history found
                                                    </p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                {/* Searches Tab */}
                                <TabsContent value="searches">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Recent Searches</CardTitle>
                                            <CardDescription>
                                                Latest {user.searches.length} searches
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-4">
                                                {user.searches.length > 0 ? (
                                                    user.searches.map((search) => (
                                                        <div key={search.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                            <div className="flex items-center gap-3">
                                                                <Search className="h-4 w-4 text-blue-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium">
                                                                        "{search.search_query}"
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        {new Date(search.created_at).toLocaleString()}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <p className="text-center text-muted-foreground py-8">
                                                        No searches found
                                                    </p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                {/* Favorites Tab */}
                                <TabsContent value="favorites">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Favorite Parts</CardTitle>
                                            <CardDescription>
                                                {user.favorites.length} favorite parts
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-4">
                                                {user.favorites.length > 0 ? (
                                                    user.favorites.map((favorite) => (
                                                        <div key={favorite.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                            <div className="flex items-center gap-3">
                                                                <Heart className="h-4 w-4 text-red-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium">
                                                                        {favorite.favoritable?.name || 'Unknown Item'}
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        {favorite.favoritable?.part_number ? (
                                                                            `Part #: ${favorite.favoritable.part_number}`
                                                                        ) : favorite.favoritable?.model_number ? (
                                                                            `Model #: ${favorite.favoritable.model_number}`
                                                                        ) : (
                                                                            `Type: ${favorite.favoritable_type?.split('\\').pop() || 'Unknown'}`
                                                                        )}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <span className="text-xs text-muted-foreground">
                                                                {new Date(favorite.created_at).toLocaleDateString()}
                                                            </span>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <p className="text-center text-muted-foreground py-8">
                                                        No favorites found
                                                    </p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </div>

            {/* Impersonation Security Check Dialog */}
            <ImpersonationSecurityCheck
                user={user}
                isOpen={showImpersonationDialog}
                onClose={() => setShowImpersonationDialog(false)}
                onConfirm={handleImpersonationConfirm}
            />
        </AppLayout>
    );
}
