import { AppContent } from '@/components/app-content';
import { AppHeader } from '@/components/app-header';
import { AppShell } from '@/components/app-shell';
import { GlobalSearchCommand } from '@/components/global-search-command';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import DynamicNavbar from '@/components/DynamicNavbar';
import { type BreadcrumbItem } from '@/types';
import { useAdmin } from '@/hooks/use-admin';
import type { PropsWithChildren } from 'react';

interface AppHeaderLayoutProps {
    breadcrumbs?: BreadcrumbItem[];
    useDynamicNavbar?: boolean;
}

export default function AppHeaderLayout({ children, breadcrumbs, useDynamicNavbar = false }: PropsWithChildren<AppHeaderLayoutProps>) {
    const isAdmin = useAdmin();

    return (
        <>
            <ImpersonationBanner />
            <AppShell>
                {useDynamicNavbar ? (
                    <DynamicNavbar />
                ) : (
                    <AppHeader breadcrumbs={breadcrumbs} />
                )}
                <AppContent>{children}</AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}
