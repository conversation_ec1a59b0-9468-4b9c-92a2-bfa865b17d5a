import AppHeaderLayout from '@/layouts/app/app-header-layout';
import DynamicFooter from '@/components/DynamicFooter';
import { type BreadcrumbItem } from '@/types';
import { type ReactNode } from 'react';

interface PublicLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

/**
 * Public Layout Component
 * 
 * This layout is designed for public-facing pages that should not include
 * the admin sidebar. It uses the header-only layout variant which provides:
 * - Top navigation header
 * - No admin sidebar
 * - Proper public page styling
 * - Responsive design
 * 
 * Used for:
 * - Public pages (/pages, /page/{slug})
 * - Any other public content that needs consistent navigation
 */
export default function PublicLayout({ children, breadcrumbs, ...props }: PublicLayoutProps) {
    return (
        <>
            <AppHeaderLayout breadcrumbs={breadcrumbs} useDynamicNavbar={true} {...props}>
                {children}
            </AppHeaderLayout>
            <DynamicFooter />
        </>
    );
}
