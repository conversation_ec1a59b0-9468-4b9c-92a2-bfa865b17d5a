import React, { useEffect, useState } from 'react';
import { Link } from '@inertiajs/react';
import { Smartphone, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AppLogo from './app-logo';

interface FooterLink {
    title: string;
    url: string;
    target: string;
}

interface SocialLink {
    platform: string;
    url: string;
    icon: string;
}

interface MenuItem {
    id: number;
    title: string;
    url: string;
    target: string;
    computed_url: string;
    children: MenuItem[];
}

interface FooterMenu {
    id: number;
    name: string;
    location: string;
    root_items: MenuItem[];
}

interface FooterConfig {
    footer_enabled: boolean;
    footer_layout: string;
    footer_background_color: string;
    footer_text_color: string;
    footer_content: string;
    footer_copyright: string;
    footer_links: FooterLink[];
    footer_social_links: SocialLink[];
    footer_show_logo: boolean;
    footer_logo_position: string;
    footer_menu_ids: number[];
    footer_menus: FooterMenu[];
    footer_newsletter_enabled: boolean;
    footer_newsletter_title: string;
    footer_newsletter_description: string;
    footer_newsletter_placeholder: string;
    footer_mobile_apps_enabled: boolean;
    footer_mobile_apps_title: string;
    footer_app_store_enabled: boolean;
    footer_app_store_url: string;
    footer_play_store_enabled: boolean;
    footer_play_store_url: string;
}

interface DynamicFooterProps {
    className?: string;
}

export default function DynamicFooter({ className = '' }: DynamicFooterProps) {
    const [config, setConfig] = useState<FooterConfig | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [email, setEmail] = useState('');
    const [subscribing, setSubscribing] = useState(false);

    useEffect(() => {
        const fetchFooterConfig = async () => {
            try {
                const response = await fetch('/api/footer-config');
                if (!response.ok) {
                    throw new Error('Failed to fetch footer configuration');
                }
                const data = await response.json();
                setConfig(data);
            } catch (err) {
                console.error('Error fetching footer config:', err);
                setError(err instanceof Error ? err.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        fetchFooterConfig();
    }, []);

    const handleNewsletterSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!email.trim()) return;

        setSubscribing(true);
        try {
            // TODO: Implement newsletter subscription API
            console.log('Newsletter subscription:', email);
            setEmail('');
            // Show success message
        } catch (err) {
            console.error('Newsletter subscription error:', err);
        } finally {
            setSubscribing(false);
        }
    };

    // Don't render anything while loading
    if (loading) {
        return null;
    }

    // Error fallback - render basic footer
    if (error) {
        return (
            <footer className={`w-full bg-gray-900 text-white py-12 ${className}`}>
                <div className="px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <div className="flex items-center justify-center space-x-2 mb-4">
                            <Smartphone className="h-8 w-8 text-blue-400" />
                            <span className="text-2xl font-bold">FixHaat</span>
                        </div>
                        <p className="text-gray-400">
                            The comprehensive mobile parts database for professionals
                        </p>
                    </div>
                </div>
            </footer>
        );
    }

    // Don't render if config is missing or footer is disabled
    if (!config || !config.footer_enabled) {
        return null;
    }

    const footerStyle = {
        backgroundColor: config.footer_background_color,
        color: config.footer_text_color,
    };

    const renderLogo = () => {
        if (!config.footer_show_logo) return null;

        return (
            <div className="flex items-center space-x-2 mb-4">
                <Smartphone className="h-8 w-8 text-blue-400" />
                <span className="text-2xl font-bold">FixHaat</span>
            </div>
        );
    };

    const renderMenuColumn = (menu: FooterMenu) => {
        if (!menu.root_items || menu.root_items.length === 0) return null;

        return (
            <div key={menu.id} className="space-y-4">
                <h3 className="text-lg font-semibold">{menu.name}</h3>
                <ul className="space-y-2">
                    {menu.root_items.map((item) => (
                        <li key={item.id}>
                            <Link
                                href={item.computed_url || item.url}
                                className="text-gray-300 hover:text-white transition-colors"
                                target={item.target}
                            >
                                {item.title}
                            </Link>
                        </li>
                    ))}
                </ul>
            </div>
        );
    };

    const renderFooterLinks = () => {
        if (!config.footer_links || config.footer_links.length === 0) return null;

        return (
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Links</h3>
                <ul className="space-y-2">
                    {config.footer_links.map((link, index) => (
                        <li key={index}>
                            <Link
                                href={link.url}
                                className="text-gray-300 hover:text-white transition-colors"
                                target={link.target}
                            >
                                {link.title}
                            </Link>
                        </li>
                    ))}
                </ul>
            </div>
        );
    };

    const renderNewsletterSection = () => {
        if (!config.footer_newsletter_enabled) return null;

        return (
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">{config.footer_newsletter_title}</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                    {config.footer_newsletter_description}
                </p>
                <form onSubmit={handleNewsletterSubmit} className="space-y-3">
                    <Input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder={config.footer_newsletter_placeholder}
                        className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                        required
                    />
                    <Button
                        type="submit"
                        disabled={subscribing}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                        {subscribing ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
                    </Button>
                </form>
                {renderSocialLinks()}
            </div>
        );
    };

    const renderSocialLinks = () => {
        if (!config.footer_social_links || config.footer_social_links.length === 0) return null;

        return (
            <div className="flex space-x-4 mt-4">
                {config.footer_social_links.map((social, index) => (
                    <a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-white transition-colors"
                        aria-label={social.platform}
                    >
                        <Mail className="h-5 w-5" />
                    </a>
                ))}
            </div>
        );
    };

    const renderMobileAppSection = () => {
        if (!config.footer_mobile_apps_enabled) return null;

        const hasAppStore = config.footer_app_store_enabled && config.footer_app_store_url;
        const hasPlayStore = config.footer_play_store_enabled && config.footer_play_store_url;

        if (!hasAppStore && !hasPlayStore) return null;

        return (
            <div className="space-y-4">
                {config.footer_mobile_apps_title && (
                    <h3 className="text-lg font-semibold">{config.footer_mobile_apps_title}</h3>
                )}
                <div className="border-t border-gray-600 pt-4">
                    <div className="flex flex-col sm:flex-row gap-3">
                        {hasAppStore && (
                            <a
                                href={config.footer_app_store_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center px-4 py-2 bg-black border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
                            >
                                <div className="flex items-center space-x-2">
                                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                                    </svg>
                                    <div className="text-left">
                                        <div className="text-xs text-gray-300">Download on the</div>
                                        <div className="text-sm font-semibold">App Store</div>
                                    </div>
                                </div>
                            </a>
                        )}
                        {hasPlayStore && (
                            <a
                                href={config.footer_play_store_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center px-4 py-2 bg-black border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
                            >
                                <div className="flex items-center space-x-2">
                                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                                    </svg>
                                    <div className="text-left">
                                        <div className="text-xs text-gray-300">Get it on</div>
                                        <div className="text-sm font-semibold">Google Play</div>
                                    </div>
                                </div>
                            </a>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    const renderModernLayout = () => {
        // Calculate grid columns based on available content
        const menuColumns = config.footer_menus?.length || 0;
        const hasFooterLinks = config.footer_links && config.footer_links.length > 0;
        const hasNewsletter = config.footer_newsletter_enabled;
        // Include company info column (always present) + menu columns + optional columns
        const totalColumns = Math.min(1 + menuColumns + (hasFooterLinks ? 1 : 0) + (hasNewsletter ? 1 : 0), 4);

        const gridCols = totalColumns === 4 ? 'lg:grid-cols-4' :
                        totalColumns === 3 ? 'lg:grid-cols-3' :
                        totalColumns === 2 ? 'lg:grid-cols-2' : 'lg:grid-cols-1';

        return (
            <div className="space-y-12">
                {/* Main footer content */}
                <div className={`grid grid-cols-1 md:grid-cols-2 ${gridCols} gap-8`}>
                    {/* Company info column */}
                    <div className="space-y-4">
                        {renderLogo()}
                        {config.footer_content && (
                            <p className="text-gray-300 text-sm leading-relaxed max-w-sm">
                                {config.footer_content}
                            </p>
                        )}
                        {/* Mobile App Download Section */}
                        {renderMobileAppSection()}
                    </div>

                    {/* Menu columns */}
                    {config.footer_menus?.map((menu) => renderMenuColumn(menu))}

                    {/* Footer links column */}
                    {hasFooterLinks && renderFooterLinks()}

                    {/* Newsletter column */}
                    {hasNewsletter && renderNewsletterSection()}
                </div>

                {/* Bottom section with copyright */}
                {config.footer_copyright && (
                    <div className="pt-8 border-t border-gray-800">
                        <p className="text-center text-gray-400 text-sm">
                            {config.footer_copyright}
                        </p>
                    </div>
                )}
            </div>
        );
    };

    return (
        <footer className={`w-full py-16 ${className}`} style={footerStyle} role="contentinfo">
            <div className="px-4 sm:px-6 lg:px-8">
                {renderModernLayout()}
            </div>
        </footer>
    );
}
