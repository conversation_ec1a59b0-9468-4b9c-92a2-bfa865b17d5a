import React, { useEffect, useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from '@/components/ui/navigation-menu';
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from '@/components/ui/sheet';
import { Menu, Search } from 'lucide-react';
import AppLogo from './app-logo';
import { type SharedData } from '@/types';

interface MenuItem {
    id: number;
    title: string;
    url: string;
    target: string;
    children?: MenuItem[];
}

interface NavbarConfig {
    navbar_enabled: boolean;
    navbar_menu_id: number | null;
    navbar_background_color: string;
    navbar_text_color: string;
    navbar_logo_position: string;
    navbar_show_search: boolean;
    navbar_sticky: boolean;
    navbar_style: string;
    menu_items: MenuItem[];
}

interface DynamicNavbarProps {
    className?: string;
    showSearch?: boolean;
    showUserMenu?: boolean;
}

export default function DynamicNavbar({ 
    className = '', 
    showSearch = true, 
    showUserMenu = true 
}: DynamicNavbarProps) {
    const [config, setConfig] = useState<NavbarConfig | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const page = usePage<SharedData>();
    const { auth } = page.props;

    useEffect(() => {
        const fetchNavbarConfig = async () => {
            try {
                const response = await fetch('/api/navbar-config');
                if (!response.ok) {
                    throw new Error('Failed to fetch navbar configuration');
                }
                const data = await response.json();
                setConfig(data);
            } catch (err) {
                console.error('Error fetching navbar config:', err);
                setError(err instanceof Error ? err.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        fetchNavbarConfig();
    }, []);

    // Return null while loading or if navbar is disabled
    if (loading || !config || !config.navbar_enabled) {
        return null;
    }

    const navbarStyle = {
        backgroundColor: config.navbar_background_color,
        color: config.navbar_text_color,
    };

    const stickyClass = config.navbar_sticky ? 'sticky top-0 z-50' : '';
    const styleClass = config.navbar_style === 'minimal' ? 'border-b-0' : 'border-b border-gray-200 dark:border-gray-700';

    const renderLogo = () => (
        <Link href="/" className="flex items-center space-x-2">
            <AppLogo />
        </Link>
    );

    const renderMenuItem = (item: MenuItem, isMobile = false) => {
        const hasChildren = item.children && item.children.length > 0;
        
        if (hasChildren) {
            // For now, render parent items as non-clickable
            return (
                <div key={item.id} className={isMobile ? "py-2" : ""}>
                    <span className={`font-medium ${isMobile ? "block text-lg" : ""}`}>
                        {item.title}
                    </span>
                    {item.children && (
                        <div className={isMobile ? "ml-4 mt-2 space-y-2" : "hidden"}>
                            {item.children.map(child => renderMenuItem(child, isMobile))}
                        </div>
                    )}
                </div>
            );
        }

        return (
            <Link
                key={item.id}
                href={item.url}
                target={item.target}
                className={`hover:opacity-80 transition-opacity ${isMobile ? "block py-2 text-lg" : ""}`}
            >
                {item.title}
            </Link>
        );
    };

    const renderDesktopMenu = () => {
        if (!config.menu_items || config.menu_items.length === 0) return null;

        return (
            <NavigationMenu className="hidden lg:flex">
                <NavigationMenuList className="space-x-6">
                    {config.menu_items.map(item => (
                        <NavigationMenuItem key={item.id}>
                            {renderMenuItem(item)}
                        </NavigationMenuItem>
                    ))}
                </NavigationMenuList>
            </NavigationMenu>
        );
    };

    const renderMobileMenu = () => {
        if (!config.menu_items || config.menu_items.length === 0) return null;

        return (
            <div className="lg:hidden">
                <Sheet>
                    <SheetTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9 rounded-lg"
                        >
                            <Menu className="h-5 w-5" />
                            <span className="sr-only">Open navigation menu</span>
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80">
                        <SheetHeader>
                            <SheetTitle>Navigation</SheetTitle>
                        </SheetHeader>
                        <div className="mt-6 space-y-4">
                            {config.menu_items.map(item => renderMenuItem(item, true))}
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        );
    };

    const renderSearchButton = () => {
        if (!config.navbar_show_search || !showSearch) return null;

        return (
            <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-lg"
                onClick={() => {
                    // Trigger global search command
                    const event = new KeyboardEvent('keydown', {
                        key: 'k',
                        ctrlKey: true,
                        metaKey: true,
                    });
                    document.dispatchEvent(event);
                }}
            >
                <Search className="h-5 w-5" />
                <span className="sr-only">Search</span>
            </Button>
        );
    };

    const renderContent = () => {
        const logoPosition = config.navbar_logo_position;
        
        return (
            <div className="mx-auto flex h-16 items-center justify-between px-4 md:max-w-7xl">
                {/* Left section */}
                <div className="flex items-center space-x-4">
                    {logoPosition === 'left' && renderLogo()}
                    {renderMobileMenu()}
                </div>

                {/* Center section */}
                <div className="flex items-center space-x-6">
                    {logoPosition === 'center' && renderLogo()}
                    {renderDesktopMenu()}
                </div>

                {/* Right section */}
                <div className="flex items-center space-x-4">
                    {logoPosition === 'right' && renderLogo()}
                    {renderSearchButton()}
                    
                    {/* User menu placeholder - can be enhanced */}
                    {showUserMenu && auth.user && (
                        <div className="text-sm">
                            Welcome, {auth.user.name}
                        </div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <nav
            className={`${stickyClass} ${styleClass} ${className}`}
            style={navbarStyle}
            role="navigation"
        >
            {renderContent()}
        </nav>
    );
}
