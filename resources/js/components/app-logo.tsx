import { useState, useEffect } from 'react';
import AppLogoIcon from './app-logo-icon';

interface SiteSettings {
    site_logo_url?: string;
    site_logo_alt?: string;
    site_logo_width?: number;
    site_logo_height?: number;
}

export default function AppLogo() {
    const [settings, setSettings] = useState<SiteSettings>({});
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Fetch public branding settings
        fetch('/api/branding')
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('Failed to fetch branding settings');
            })
            .then(data => {
                setSettings(data);
                setLoading(false);
            })
            .catch(() => {
                setLoading(false);
            });
    }, []);

    const logoUrl = settings.site_logo_url;
    const logoAlt = settings.site_logo_alt || 'FixHaat';
    const logoWidth = settings.site_logo_width || 32;
    const logoHeight = settings.site_logo_height || 32;

    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground">
                {!loading && logoUrl ? (
                    <img
                        src={logoUrl}
                        alt={logoAlt}
                        style={{
                            width: `${Math.min(logoWidth, 32)}px`,
                            height: `${Math.min(logoHeight, 32)}px`,
                            objectFit: 'contain'
                        }}
                        className="max-w-full max-h-full"
                    />
                ) : (
                    <AppLogoIcon className="size-5 fill-current text-white dark:text-black" />
                )}
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                <span className="mb-0.5 truncate leading-tight font-semibold">FixHaat</span>
            </div>
        </>
    );
}
