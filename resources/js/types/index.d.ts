import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface Category {
    id: number;
    name: string;
    slug?: string;
    description?: string;
    parent_id?: number;
    is_active: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    categories?: Category[];
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    subscription_plan?: string;
    isAdmin?: boolean;
    [key: string]: unknown; // This allows for additional properties...
}

export interface PricingPlan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    formatted_price: string;
    metadata?: Record<string, unknown>;

    // Payment method configurations
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    crypto_payment_enabled: boolean;

    // Paddle integration
    paddle_price_id_monthly?: string;
    paddle_price_id_yearly?: string;
    paddle_product_id?: string;
    has_paddle_integration: boolean;

    // ShurjoPay integration
    shurjopay_price_id_monthly?: string;
    shurjopay_price_id_yearly?: string;
    shurjopay_product_id?: string;
    has_shurjopay_integration: boolean;

    // Coinbase Commerce integration
    coinbase_commerce_price_id_monthly?: string;
    coinbase_commerce_price_id_yearly?: string;
    coinbase_commerce_product_id?: string;
    has_coinbase_commerce_integration: boolean;

    // Billing cycle support
    supports_monthly: boolean;
    supports_yearly: boolean;
    supports_coinbase_commerce_monthly?: boolean;
    supports_coinbase_commerce_yearly?: boolean;

    // Payment method availability
    supports_online_payment: boolean;
    supports_crypto_payment: boolean;
    has_online_payment_enabled: boolean;
    has_offline_payment_enabled: boolean;
    has_crypto_payment_enabled: boolean;
    has_any_payment_method: boolean;

    // Fee configuration
    paddle_fee_percentage?: number;
    paddle_fee_fixed?: number;
    shurjopay_fee_percentage?: number;
    shurjopay_fee_fixed?: number;
    coinbase_commerce_fee_percentage?: number;
    coinbase_commerce_fee_fixed?: number;
    offline_fee_percentage?: number;
    offline_fee_fixed?: number;
    fee_handling?: string;
    show_fees_breakdown?: boolean;
    tax_percentage?: number;
    tax_inclusive?: boolean;
    has_fees_configured?: boolean;

    // Timestamps
    created_at?: string;
    updated_at?: string;
    subscriptions_count?: number;
}

export interface UserSearch {
    id: number;
    user_id: number;
    search_query: string;
    search_type: string;
    results_count: number;
    created_at: string;
    user?: User;
}
