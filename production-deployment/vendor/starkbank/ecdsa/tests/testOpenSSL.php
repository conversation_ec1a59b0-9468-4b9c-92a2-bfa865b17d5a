<?php

namespace EllipticCurve\Test;
use EllipticCurve\Test\TestCase;

echo "\n\nRunning OpenSSL tests:";
\Test\printHeader("OpenSSL");


class TestOpenSSL extends TestCase
{
    public function testDerConversion()
    {
        // Generated by: openssl ecparam -name secp256k1 -genkey -out privateKey.pem
        $privateKeyPem = \EllipticCurve\Utils\File::read("tests/privateKey.pem");

        $privateKey = \EllipticCurve\PrivateKey::fromPem($privateKeyPem);

        $message = \EllipticCurve\Utils\File::read("tests/message.txt");

        $signature = \EllipticCurve\Ecdsa::sign($message, $privateKey);

        $publicKey = $privateKey->publicKey();

        \Test\assertEqual(\EllipticCurve\Ecdsa::verify($message, $signature, $publicKey), true);
    }

    public function testBase64Conversion()
    {
        // openssl ec -in privateKey.pem -pubout -out publicKey.pem
        $publicKeyPem = \EllipticCurve\Utils\File::read("tests/publicKey.pem");

        // openssl dgst -sha256 -sign privateKey.pem -out signature.binary message.txt
        $signatureDer = \EllipticCurve\Utils\File::read("tests/signatureDer.txt", "rb");

        $message = \EllipticCurve\Utils\File::read("tests/message.txt");

        $publicKey = \EllipticCurve\PublicKey::fromPem($publicKeyPem);

        $signature = \EllipticCurve\Signature::fromDer($signatureDer);

        \Test\assertEqual(\EllipticCurve\Ecdsa::verify($message, $signature, $publicKey), true);
    }
}


$tests = new TestOpenSSL();
$tests->run();
