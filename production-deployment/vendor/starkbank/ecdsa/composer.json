{"name": "starkbank/ecdsa", "version": "2.1.0", "type": "library", "description": "fast openSSL-compatible implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA)", "homepage": "https://github.com/starkbank/ecdsa-php", "license": "MIT", "authors": [{"name": "StarkBank", "email": "<EMAIL>", "homepage": "https://starkbank.com", "role": "Developer"}], "require": {"php": ">=7.0", "ext-gmp": "*"}, "autoload": {"files": ["src/ellipticcurve.php"], "classmap": ["src/"], "psr-4": {"EllipticCurve\\": "src/"}}}