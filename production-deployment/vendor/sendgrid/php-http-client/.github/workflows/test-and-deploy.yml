name: Test and Deploy
on:
  push:
    branches: [ '*' ]
    tags: [ '*' ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run automatically at 8AM PST Monday-Friday
    - cron: '0 15 * * 1-5'
  workflow_dispatch:

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        php: [ '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4' ]
        dependencies:
          - "lowest"
          - "highest"
    steps:
      - name: Checkout php-http-client
        uses: actions/checkout@v2

      - name: Setup PHP Action
        uses: shivammathur/setup-php@2.15.0
        with:
          php-version: ${{ matrix.php }}
        id: php

      - name: Composer webhook config
        run: composer config -g github-oauth.github.com ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        run: composer install

      - name: Update Dependencies
        if: ${{ matrix.dependencies == 'lowest' }}
        run: composer update --prefer-lowest --prefer-stable -n

      - name: Run Tests
        run: make test

  deploy:
    name: Deploy
    if: success() && github.ref_type == 'tag'
    needs: [ test ]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout php-http-client
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@2.15.0
        with:
          php-version: '8.1'
        id: php

      - name: Build Release Artifacts
        run: make bundle

      - name: Create GitHub Release
        uses: sendgrid/dx-automator/actions/release@main
        with:
          assets: php-http-client.zip
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Submit metric to Datadog
        uses: sendgrid/dx-automator/actions/datadog-release-metric@main
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}

  notify-on-failure:
    name: Slack notify on failure
    if: failure() && github.event_name != 'pull_request' && (github.ref == 'refs/heads/main' || github.ref_type == 'tag')
    needs: [ test, deploy ]
    runs-on: ubuntu-latest
    steps:
      - uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_COLOR: failure
          SLACK_ICON_EMOJI: ':github:'
          SLACK_MESSAGE: ${{ format('Test *{0}*, Deploy *{1}*, {2}/{3}/actions/runs/{4}', needs.test.result, needs.deploy.result, github.server_url, github.repository, github.run_id) }}
          SLACK_TITLE: Action Failure - ${{ github.repository }}
          SLACK_USERNAME: GitHub Actions
          SLACK_MSG_AUTHOR: twilio-dx
          SLACK_FOOTER: Posted automatically using GitHub Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          MSG_MINIMAL: true
