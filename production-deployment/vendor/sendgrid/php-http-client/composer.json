{"name": "sendgrid/php-http-client", "description": "HTTP REST client, simplified for PHP", "type": "library", "homepage": "http://github.com/sendgrid/php-http-client", "keywords": ["SendGrid", "HTTP", "REST", "API", "Fluent"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.3", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "~2.0", "friendsofphp/php-cs-fixer": "^2.16"}, "suggest": {"composer/ca-bundle": "Including this library will ensure that a valid CA bundle is available for secure connections"}, "autoload": {"psr-4": {"SendGrid\\": "lib/"}}, "autoload-dev": {"psr-4": {"SendGrid\\Test\\": "test/unit/"}}}