{"name": "sendgrid/sendgrid", "description": "This library allows you to quickly and easily send emails through Twilio SendGrid using PHP.", "homepage": "http://github.com/sendgrid/sendgrid-php", "license": "MIT", "keywords": ["SendGrid", "sendgrid", "<PERSON><PERSON><PERSON>", "twi<PERSON>", "email", "send", "grid"], "require": {"php": ">=7.3", "sendgrid/php-http-client": "~4.1", "starkbank/ecdsa": "^2.1.0", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-gmp": "*"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "3.*", "swaggest/json-diff": "^3.4", "phpstan/phpstan": "^1.5"}, "replace": {"sendgrid/sendgrid-php": "*"}, "type": "library", "autoload": {"psr-4": {"SendGrid\\Contacts\\": "lib/contacts/", "SendGrid\\EventWebhook\\": "lib/eventwebhook/", "SendGrid\\Helper\\": "lib/helper/", "SendGrid\\Mail\\": "lib/mail/", "SendGrid\\Stats\\": "lib/stats/"}, "classmap": ["lib/BaseSendGridClientInterface.php", "lib/SendGrid.php", "lib/TwilioEmail.php"]}, "autoload-dev": {"psr-4": {"SendGrid\\Tests\\Integration\\": "test/integration", "SendGrid\\Tests\\Unit\\": "test/unit"}, "classmap": ["test/BaseTestClass.php"]}}