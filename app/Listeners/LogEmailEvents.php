<?php

namespace App\Listeners;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use App\Services\EmailTrackingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Str;

class LogEmailEvents implements ShouldQueue
{
    use InteractsWithQueue;

    protected EmailTrackingService $trackingService;

    /**
     * Create the event listener.
     */
    public function __construct(EmailTrackingService $trackingService)
    {
        $this->trackingService = $trackingService;
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        try {
            if ($event instanceof MessageSending) {
                $this->handleMessageSending($event);
            } elseif ($event instanceof MessageSent) {
                $this->handleMessageSent($event);
            }
        } catch (\Exception $e) {
            Log::error('Failed to handle email event', [
                'event_type' => get_class($event),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle message sending event.
     */
    protected function handleMessageSending(MessageSending $event): void
    {
        try {
            // Generate unique message ID for tracking
            $messageId = Str::uuid()->toString();

            // Add message ID header for tracking
            $event->message->getHeaders()->addTextHeader('X-Message-ID', $messageId);

            // Extract recipient information
            $to = $event->message->getTo();
            $toEmail = null;
            $toName = null;
            $toEmails = [];

            if ($to && count($to) > 0) {
                foreach ($to as $address) {
                    $toEmails[] = $address->getAddress();
                }
                $firstToAddress = reset($to);
                $toEmail = $firstToAddress->getAddress();
                $toName = $firstToAddress->getName();
            }

            // Extract sender information
            $from = $event->message->getFrom();
            $fromEmail = config('mail.from.address');
            $fromName = config('mail.from.name');

            if ($from && count($from) > 0) {
                $firstFromAddress = reset($from);
                $fromEmail = $firstFromAddress->getAddress();
                $fromName = $firstFromAddress->getName();
            }

            // Create email log entry
            $emailLog = EmailLog::create([
                'message_id' => $messageId,
                'to_email' => $toEmail,
                'to_name' => $toName,
                'from_email' => $fromEmail,
                'from_name' => $fromName,
                'subject' => $event->message->getSubject() ?? 'No Subject',
                'provider' => config('mail.default'),
                'status' => 'pending',
                'sent_at' => null,
            ]);

            // Inject tracking into email content
            $this->injectTracking($event->message, $messageId);

            Log::debug('Email sending with tracking', [
                'message_id' => $messageId,
                'to' => $toEmails,
                'subject' => $event->message->getSubject(),
                'provider' => config('mail.default'),
                'email_log_id' => $emailLog->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to handle MessageSending event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle message sent event.
     */
    protected function handleMessageSent(MessageSent $event): void
    {
        try {
            // This event fires after the email is successfully sent
            $message = $event->sent;

            // Try to get message ID from our custom header first
            $messageId = null;
            $headers = $event->message->getHeaders();

            if ($headers->has('X-Message-ID')) {
                $messageId = $headers->get('X-Message-ID')->getBodyAsString();
            }

            // Fall back to the message ID from the sent message
            if (!$messageId) {
                $messageId = $message->getMessageId();
            }

            if (!$messageId) {
                Log::warning('Email sent but no message ID available for tracking');
                return;
            }

            // Find email log entry
            $emailLog = EmailLog::where('message_id', $messageId)->first();

            if (!$emailLog) {
                // Create a basic email log if it doesn't exist (fallback)
                $to = $event->message->getTo();
                $toEmail = null;
                $toName = null;

                if ($to && count($to) > 0) {
                    $firstToAddress = reset($to);
                    $toEmail = $firstToAddress->getAddress();
                    $toName = $firstToAddress->getName();
                }

                $from = $event->message->getFrom();
                $fromEmail = config('mail.from.address');
                $fromName = config('mail.from.name');

                if ($from && count($from) > 0) {
                    $firstFromAddress = reset($from);
                    $fromEmail = $firstFromAddress->getAddress();
                    $fromName = $firstFromAddress->getName();
                }

                $emailLog = EmailLog::create([
                    'message_id' => $messageId,
                    'to_email' => $toEmail,
                    'to_name' => $toName,
                    'from_email' => $fromEmail,
                    'from_name' => $fromName,
                    'subject' => $event->message->getSubject() ?? 'No Subject',
                    'provider' => config('mail.default'),
                    'status' => 'sent',
                    'sent_at' => now(),
                ]);
            } else {
                // Update existing log
                $emailLog->markAsSent();
            }

            // Create sent event if it doesn't exist
            $existingEvent = EmailEvent::where('email_log_id', $emailLog->id)
                ->where('event_type', 'sent')
                ->first();

            if (!$existingEvent) {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'sent',
                    'event_timestamp' => now(),
                    'event_data' => [
                        'message_id' => $messageId,
                        'provider' => config('mail.default'),
                    ],
                ]);
            }

            Log::info('Email sent event logged', [
                'email_log_id' => $emailLog->id,
                'message_id' => $messageId,
                'to_email' => $emailLog->to_email,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to handle MessageSent event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Inject tracking pixels and link tracking into email content.
     */
    protected function injectTracking($message, string $messageId): void
    {
        try {
            // Get the email body
            $body = $message->getBody();

            if (!$body) {
                return;
            }

            // Convert body to string if it's not already
            $htmlContent = (string) $body;

            // Only inject tracking into HTML emails
            if (strpos($htmlContent, '<html') === false && strpos($htmlContent, '<body') === false) {
                return;
            }

            // Generate tracking pixel
            $trackingPixel = $this->trackingService->generateTrackingPixel($messageId);

            // Inject tracking pixel before closing body tag
            if (strpos($htmlContent, '</body>') !== false) {
                $htmlContent = str_replace('</body>', $trackingPixel . '</body>', $htmlContent);
            } else {
                // If no body tag, append to end
                $htmlContent .= $trackingPixel;
            }

            // Track links in the content
            $htmlContent = $this->trackingService->wrapLinksWithTracking($htmlContent, $messageId);

            // Update the message body
            $message->setBody($htmlContent, 'text/html');

            Log::debug('Tracking injected into email', [
                'message_id' => $messageId,
                'has_tracking_pixel' => true,
                'has_link_tracking' => true,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to inject tracking into email', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
