<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use SendGrid\Mail\Mail as SendGridMail;
use SendGrid;

class EmailService
{
    protected EmailTrackingService $trackingService;

    public function __construct(EmailTrackingService $trackingService)
    {
        $this->trackingService = $trackingService;
    }

    /**
     * Send email using the configured provider.
     */
    public function send($mailable, $to, array $options = []): bool
    {
        try {
            $provider = config('mail.default');

            Log::info('Attempting to send email', [
                'provider' => $provider,
                'to' => $to,
                'mailable' => get_class($mailable)
            ]);

            switch ($provider) {
                case 'sendgrid':
                    return $this->sendViaSendGrid($mailable, $to, $options);
                case 'log':
                    return $this->sendViaLog($mailable, $to, $options);
                case 'smtp':
                default:
                    return $this->sendViaSmtp($mailable, $to, $options);
            }
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'to' => $to,
                'mailable' => get_class($mailable),
                'provider' => config('mail.default'),
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'config' => [
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'username' => config('mail.mailers.smtp.username') ? 'configured' : 'not configured',
                    'password' => config('mail.mailers.smtp.password') ? 'configured' : 'not configured',
                ]
            ]);

            return false;
        }
    }

    /**
     * Send email via SMTP.
     */
    private function sendViaSmtp($mailable, $to, array $options = []): bool
    {
        try {
            // Generate message ID for tracking
            $messageId = \Illuminate\Support\Str::uuid()->toString();

            // Get the rendered content with tracking
            $content = $this->renderMailable($mailable, $messageId);

            // Create a custom mailable with tracking injected
            $trackingMailable = new class($mailable, $content, $messageId) extends \Illuminate\Mail\Mailable {
                public function __construct(
                    private $originalMailable,
                    private array $trackedContent,
                    private string $messageId
                ) {}

                public function envelope(): \Illuminate\Mail\Mailables\Envelope
                {
                    return $this->originalMailable->envelope();
                }

                public function content(): \Illuminate\Mail\Mailables\Content
                {
                    return new \Illuminate\Mail\Mailables\Content(
                        htmlString: $this->trackedContent['html'],
                        text: $this->trackedContent['text'] ?? null,
                    );
                }

                public function attachments(): array
                {
                    return method_exists($this->originalMailable, 'attachments')
                        ? $this->originalMailable->attachments()
                        : [];
                }
            };

            // Use Laravel's Mail facade which will trigger the event listeners
            Mail::to($to)->send($trackingMailable);

            // Log the email manually to ensure tracking
            $this->logEmailSent($mailable, $to, 'smtp', array_merge($options, [
                'message_id' => $messageId
            ]));

            Log::info('Email sent via SMTP successfully with tracking', [
                'to' => $to,
                'mailable' => get_class($mailable),
                'message_id' => $messageId,
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port')
            ]);

            return true;
        } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
            Log::error('SMTP transport error', [
                'to' => $to,
                'error' => $e->getMessage(),
                'error_type' => 'transport',
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'encryption' => config('mail.mailers.smtp.encryption')
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('SMTP email sending failed', [
                'to' => $to,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Test SMTP connection using Symfony Mailer.
     */
    private function testSmtpConnection(): void
    {
        // In Symfony Mailer, we test connection by attempting to send a test message
        // The actual connection test is handled internally by Symfony Mailer
        $mailer = app('mailer');

        // Get the transport for the current mailer
        $transport = $mailer->getSymfonyTransport();

        // Test the connection by pinging the transport
        if (method_exists($transport, 'ping')) {
            $transport->ping();
        }
    }

    /**
     * Send email via SendGrid.
     */
    private function sendViaSendGrid($mailable, $to, array $options = []): bool
    {
        try {
            $sendgrid = new SendGrid(config('services.sendgrid.api_key'));

            // Generate message ID for tracking
            $messageId = \Illuminate\Support\Str::uuid()->toString();

            // Build the email
            $email = new SendGridMail();
            $email->setFrom(config('mail.from.address'), config('mail.from.name'));
            $email->setSubject($mailable->envelope()->subject);
            $email->addTo($to);

            // Get the rendered content with tracking
            $content = $this->renderMailable($mailable, $messageId);
            $email->addContent("text/html", $content['html']);

            if (isset($content['text'])) {
                $email->addContent("text/plain", $content['text']);
            }

            // Add custom headers if provided
            if (isset($options['headers'])) {
                foreach ($options['headers'] as $key => $value) {
                    $email->addHeader($key, $value);
                }
            }

            // Send the email
            $response = $sendgrid->send($email);

            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                // Log the email manually since SendGrid bypasses Laravel's mail events
                $options['message_id'] = $messageId;
                $this->logEmailSent($mailable, $to, 'sendgrid', $options);

                Log::info('Email sent via SendGrid', [
                    'to' => $to,
                    'mailable' => get_class($mailable),
                    'status_code' => $response->statusCode()
                ]);

                return true;
            } else {
                // Log the failed email
                $this->logEmailFailed($mailable, $to, 'sendgrid', $response->body(), $options);

                Log::error('SendGrid email sending failed', [
                    'to' => $to,
                    'status_code' => $response->statusCode(),
                    'body' => $response->body()
                ]);

                return false;
            }
        } catch (\Exception $e) {
            // Log the failed email
            $this->logEmailFailed($mailable, $to, 'sendgrid', $e->getMessage(), $options);

            Log::error('SendGrid email sending failed', [
                'to' => $to,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send email via log (for testing).
     */
    private function sendViaLog($mailable, $to, array $options = []): bool
    {
        try {
            // Generate message ID for tracking
            $messageId = \Illuminate\Support\Str::uuid()->toString();

            // Get the rendered content with tracking
            $content = $this->renderMailable($mailable, $messageId);

            // Create a custom mailable with tracking injected
            $trackingMailable = new class($mailable, $content, $messageId) extends \Illuminate\Mail\Mailable {
                public function __construct(
                    private $originalMailable,
                    private array $trackedContent,
                    private string $messageId
                ) {}

                public function envelope(): \Illuminate\Mail\Mailables\Envelope
                {
                    return $this->originalMailable->envelope();
                }

                public function content(): \Illuminate\Mail\Mailables\Content
                {
                    return new \Illuminate\Mail\Mailables\Content(
                        htmlString: $this->trackedContent['html'],
                        text: $this->trackedContent['text'] ?? null,
                    );
                }

                public function attachments(): array
                {
                    return method_exists($this->originalMailable, 'attachments')
                        ? $this->originalMailable->attachments()
                        : [];
                }
            };

            // Use Laravel's Mail facade which will trigger the event listeners
            Mail::to($to)->send($trackingMailable);

            // Log the email manually to ensure tracking
            $this->logEmailSent($mailable, $to, 'log', array_merge($options, [
                'message_id' => $messageId
            ]));

            Log::info('Email sent via log driver with tracking', [
                'to' => $to,
                'mailable' => get_class($mailable),
                'message_id' => $messageId,
                'subject' => $mailable->envelope()->subject
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Log email sending failed', [
                'to' => $to,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Render mailable content.
     */
    private function renderMailable($mailable, ?string $messageId = null): array
    {
        $content = $mailable->content();
        $data = $content->with ?? [];

        $html = view($content->html, $data)->render();
        $text = null;

        if ($content->text) {
            $text = view($content->text, $data)->render();
        }

        // Add tracking to HTML content if message ID is provided
        if ($messageId && $html) {
            $html = $this->trackingService->addTrackingToEmail($html, $messageId);
        }

        return [
            'html' => $html,
            'text' => $text
        ];
    }

    /**
     * Test email configuration.
     */
    public function testConfiguration(?string $provider = null): array
    {
        $provider = $provider ?? config('mail.default');
        
        try {
            switch ($provider) {
                case 'sendgrid':
                    return $this->testSendGridConfiguration();
                case 'smtp':
                default:
                    return $this->testSmtpConfiguration();
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Configuration test failed: ' . $e->getMessage(),
                'provider' => $provider
            ];
        }
    }

    /**
     * Test SMTP configuration.
     */
    private function testSmtpConfiguration(): array
    {
        try {
            // Validate configuration first
            $host = config('mail.mailers.smtp.host');
            $port = config('mail.mailers.smtp.port');
            $username = config('mail.mailers.smtp.username');
            $password = config('mail.mailers.smtp.password');
            $encryption = config('mail.mailers.smtp.encryption');

            if (empty($host) || empty($port)) {
                return [
                    'success' => false,
                    'message' => 'SMTP configuration is incomplete. Please provide host and port.',
                    'provider' => 'smtp',
                    'details' => [
                        'host' => $host ?: 'missing',
                        'port' => $port ?: 'missing',
                        'encryption' => $encryption ?: 'none'
                    ]
                ];
            }

            // Check if this is a local SMTP server (like Mailpit, MailHog)
            $isLocalSmtp = in_array($host, ['localhost', '127.0.0.1', '0.0.0.0']) ||
                          in_array($port, [1025, 1080, 8025]) || // Common local SMTP ports
                          strpos($host, '.local') !== false;

            // For non-local SMTP servers, require username and password
            if (!$isLocalSmtp && (empty($username) || empty($password))) {
                return [
                    'success' => false,
                    'message' => 'SMTP configuration is incomplete. Please provide username and password for external SMTP servers.',
                    'provider' => 'smtp',
                    'details' => [
                        'host' => $host,
                        'port' => $port,
                        'username' => $username ? 'configured' : 'missing',
                        'password' => $password ? 'configured' : 'missing',
                        'encryption' => $encryption ?: 'none',
                        'is_local_smtp' => $isLocalSmtp
                    ]
                ];
            }

            // Test connection with timeout and retry logic
            $timeout = config('mail.mailers.smtp.timeout', 5);
            $maxRetries = 2;
            $retryDelay = 1; // seconds
            $lastException = null;

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                try {
                    Log::info("SMTP connection test attempt {$attempt}/{$maxRetries}", [
                        'host' => $host,
                        'port' => $port,
                        'timeout' => $timeout
                    ]);

                    // Set a shorter timeout for testing
                    $originalTimeout = ini_get('default_socket_timeout');
                    ini_set('default_socket_timeout', $timeout);

                    // Use Laravel's Mail facade to test the connection
                    Mail::raw('SMTP Connection Test', function ($message) {
                        $message->to('<EMAIL>')
                               ->subject('SMTP Connection Test');
                    });

                    // Restore original timeout
                    ini_set('default_socket_timeout', $originalTimeout);

                    // If we get here without an exception, the SMTP connection is working
                    return [
                        'success' => true,
                        'message' => 'SMTP configuration is valid and connection successful',
                        'provider' => 'smtp',
                        'details' => [
                            'host' => $host,
                            'port' => $port,
                            'encryption' => $encryption,
                            'username' => $username,
                            'connection_test' => 'passed',
                            'attempts' => $attempt,
                            'timeout' => $timeout
                        ]
                    ];

                } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
                    // Restore original timeout
                    if (isset($originalTimeout)) {
                        ini_set('default_socket_timeout', $originalTimeout);
                    }

                    $lastException = $e;

                    // Check if the error is about the invalid email domain (which means SMTP connection worked)
                    if (strpos($e->getMessage(), 'nonexistent-domain-for-testing.invalid') !== false ||
                        strpos($e->getMessage(), 'No MX record') !== false ||
                        strpos($e->getMessage(), 'Domain not found') !== false) {
                        // SMTP connection worked, just the test domain doesn't exist (which is expected)
                        return [
                            'success' => true,
                            'message' => 'SMTP configuration is valid and connection successful',
                            'provider' => 'smtp',
                            'details' => [
                                'host' => $host,
                                'port' => $port,
                                'encryption' => $encryption,
                                'username' => $username,
                                'connection_test' => 'passed',
                                'attempts' => $attempt,
                                'timeout' => $timeout
                            ]
                        ];
                    }

                    // Check if this is a timeout or connection error that we should retry
                    if ($this->shouldRetrySmtpConnection($e) && $attempt < $maxRetries) {
                        Log::warning("SMTP connection attempt {$attempt} failed, retrying in {$retryDelay}s", [
                            'error' => $e->getMessage(),
                            'host' => $host,
                            'port' => $port
                        ]);
                        sleep($retryDelay);
                        continue;
                    }

                    // Final attempt failed or non-retryable error
                    break;

                } catch (\Exception $e) {
                    // Restore original timeout
                    if (isset($originalTimeout)) {
                        ini_set('default_socket_timeout', $originalTimeout);
                    }

                    $lastException = $e;

                    // For non-transport exceptions, don't retry
                    break;
                }
            }

            // All attempts failed - provide helpful troubleshooting guidance
            $troubleshootingMessage = $this->getSmtpTroubleshootingMessage($host, $lastException);

            return [
                'success' => false,
                'message' => 'SMTP connection failed after ' . $maxRetries . ' attempts: ' . ($lastException ? $lastException->getMessage() : 'Unknown error'),
                'provider' => 'smtp',
                'error_type' => $lastException ? get_class($lastException) : 'unknown',
                'troubleshooting' => $troubleshootingMessage,
                'details' => [
                    'host' => $host,
                    'port' => $port,
                    'encryption' => $encryption,
                    'attempts' => $maxRetries,
                    'timeout' => $timeout,
                    'error_details' => $lastException ? $lastException->getMessage() : 'Unknown error'
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'SMTP configuration test failed: ' . $e->getMessage(),
                'provider' => 'smtp',
                'error_type' => get_class($e),
                'details' => [
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'error_details' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Test SendGrid configuration.
     */
    private function testSendGridConfiguration(): array
    {
        try {
            $apiKey = config('services.sendgrid.api_key');
            
            if (!$apiKey) {
                throw new \Exception('SendGrid API key not configured');
            }
            
            $sendgrid = new SendGrid($apiKey);
            
            // Test API key validity by making a simple API call
            $response = $sendgrid->client->user()->get();
            
            if ($response->statusCode() === 200) {
                return [
                    'success' => true,
                    'message' => 'SendGrid configuration is valid',
                    'provider' => 'sendgrid',
                    'api_key_prefix' => substr($apiKey, 0, 10) . '...'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'SendGrid API key is invalid',
                    'provider' => 'sendgrid',
                    'status_code' => $response->statusCode()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'SendGrid configuration test failed: ' . $e->getMessage(),
                'provider' => 'sendgrid'
            ];
        }
    }

    /**
     * Get email statistics.
     */
    public function getEmailStatistics(int $days = 30): array
    {
        // Use the EmailAnalyticsService to get real statistics
        $analyticsService = app(EmailAnalyticsService::class);
        return $analyticsService->getEmailStatistics($days);
    }

    /**
     * Validate email address.
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Get email provider status.
     */
    public function getProviderStatus(): array
    {
        $provider = config('mail.default');
        
        return [
            'provider' => $provider,
            'configured' => $this->isProviderConfigured($provider),
            'status' => $this->getProviderHealthStatus($provider)
        ];
    }

    /**
     * Check if email provider is properly configured.
     */
    private function isProviderConfigured(string $provider): bool
    {
        switch ($provider) {
            case 'sendgrid':
                return !empty(config('services.sendgrid.api_key'));
            case 'smtp':
                return !empty(config('mail.mailers.smtp.host')) && 
                       !empty(config('mail.mailers.smtp.port'));
            default:
                return false;
        }
    }

    /**
     * Get provider health status.
     * This method performs a lightweight check without making actual connections.
     * For real connectivity testing, use testConfiguration() method explicitly.
     */
    private function getProviderHealthStatus(string $provider): string
    {
        // Only check if configuration is present, don't test actual connectivity
        // This prevents SMTP connection attempts during page load
        switch ($provider) {
            case 'sendgrid':
                return !empty(config('services.sendgrid.api_key')) ? 'configured' : 'not_configured';
            case 'smtp':
                $host = config('mail.mailers.smtp.host');
                $port = config('mail.mailers.smtp.port');
                $username = config('mail.mailers.smtp.username');
                $password = config('mail.mailers.smtp.password');

                if (empty($host) || empty($port)) {
                    return 'not_configured';
                }

                // Check if this is a local SMTP server (like Mailpit, MailHog)
                $isLocalSmtp = in_array($host, ['localhost', '127.0.0.1', '0.0.0.0']) ||
                              in_array($port, [1025, 1080, 8025]) || // Common local SMTP ports
                              strpos($host, '.local') !== false;

                // For local SMTP servers, username/password are not required
                if ($isLocalSmtp) {
                    return 'configured';
                }

                // For external SMTP servers, check if we have all required configuration
                if (empty($username) || empty($password)) {
                    return 'incomplete';
                }

                // Configuration appears complete but we haven't tested connectivity
                return 'configured';
            case 'log':
                return 'healthy'; // Log driver is always healthy
            default:
                return 'unknown';
        }
    }

    /**
     * Test provider connectivity (performs actual connection test).
     * This method should be called explicitly when testing is needed,
     * not during regular page loads.
     */
    public function testProviderConnectivity(string $provider): array
    {
        return $this->testConfiguration($provider);
    }

    /**
     * Determine if an SMTP connection error should be retried.
     */
    private function shouldRetrySmtpConnection(\Exception $e): bool
    {
        $retryableErrors = [
            'Connection timed out',
            'Connection refused',
            'Network is unreachable',
            'Temporary failure',
            'Resource temporarily unavailable',
            'Operation timed out',
            'Connection reset by peer'
        ];

        $errorMessage = $e->getMessage();

        foreach ($retryableErrors as $retryableError) {
            if (stripos($errorMessage, $retryableError) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get troubleshooting message for SMTP connection issues.
     */
    private function getSmtpTroubleshootingMessage(string $host, ?\Exception $exception): array
    {
        $messages = [];

        if (stripos($host, 'gmail.com') !== false) {
            $messages[] = "Gmail SMTP Configuration:";
            $messages[] = "• Enable 2-Factor Authentication on your Gmail account";
            $messages[] = "• Generate an App Password: https://myaccount.google.com/apppasswords";
            $messages[] = "• Use the App Password instead of your regular Gmail password";
            $messages[] = "• Ensure 'Less secure app access' is enabled (if not using App Password)";
            $messages[] = "• Verify the email address and credentials are correct";
        } elseif (stripos($host, 'outlook.com') !== false || stripos($host, 'hotmail.com') !== false) {
            $messages[] = "Outlook/Hotmail SMTP Configuration:";
            $messages[] = "• Use smtp-mail.outlook.com as the host";
            $messages[] = "• Port 587 with STARTTLS encryption";
            $messages[] = "• Enable 2-Factor Authentication and use App Password";
            $messages[] = "• Verify account settings allow SMTP access";
        } else {
            $messages[] = "General SMTP Troubleshooting:";
            $messages[] = "• Verify the SMTP host and port are correct";
            $messages[] = "• Check if the email provider requires authentication";
            $messages[] = "• Ensure the username and password are valid";
            $messages[] = "• Try different encryption methods (TLS, SSL, or none)";
        }

        if ($exception) {
            $errorMessage = $exception->getMessage();

            if (stripos($errorMessage, 'timed out') !== false) {
                $messages[] = "";
                $messages[] = "Connection Timeout Issues:";
                $messages[] = "• Check your internet connection";
                $messages[] = "• Verify firewall settings allow SMTP connections";
                $messages[] = "• Try using port 465 (SSL) instead of 587 (TLS)";
                $messages[] = "• Contact your hosting provider about SMTP restrictions";
            } elseif (stripos($errorMessage, 'authentication') !== false) {
                $messages[] = "";
                $messages[] = "Authentication Issues:";
                $messages[] = "• Double-check username and password";
                $messages[] = "• Ensure the account allows SMTP access";
                $messages[] = "• Try using the full email address as username";
            } elseif (stripos($errorMessage, 'refused') !== false) {
                $messages[] = "";
                $messages[] = "Connection Refused:";
                $messages[] = "• Verify the SMTP host and port are correct";
                $messages[] = "• Check if your IP is blocked by the email provider";
                $messages[] = "• Try using a different port (25, 465, 587, 2525)";
            }
        }

        return $messages;
    }

    /**
     * Manually log a sent email (for providers that bypass Laravel's mail events).
     */
    private function logEmailSent($mailable, string $to, string $provider, array $options = []): void
    {
        try {
            $messageId = $options['message_id'] ?? \Illuminate\Support\Str::uuid()->toString();

            // Extract email details
            $subject = $mailable->envelope()->subject ?? 'No Subject';
            $contentPreview = $this->getContentPreview($mailable);

            // Create email log entry
            $emailLog = \App\Models\EmailLog::create([
                'message_id' => $messageId,
                'to_email' => $to,
                'to_name' => $options['to_name'] ?? null,
                'from_email' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
                'subject' => $subject,
                'content_preview' => $contentPreview,
                'provider' => $provider,
                'status' => 'sent',
                'metadata' => [
                    'headers' => $options['headers'] ?? [],
                    'options' => $options,
                ],
                'mailable_class' => get_class($mailable),
                'user_id' => $options['user_id'] ?? null,
                'sent_at' => now(),
            ]);

            // Create sent event
            \App\Models\EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => 'sent',
                'event_timestamp' => now(),
                'event_data' => [
                    'message_id' => $messageId,
                    'provider' => $provider,
                ],
            ]);

            Log::info('Email logged manually', [
                'email_log_id' => $emailLog->id,
                'message_id' => $messageId,
                'to_email' => $to,
                'provider' => $provider,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log sent email', [
                'to' => $to,
                'provider' => $provider,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Manually log a failed email.
     */
    private function logEmailFailed($mailable, string $to, string $provider, string $failureReason, array $options = []): void
    {
        try {
            $messageId = \Illuminate\Support\Str::uuid()->toString();

            // Extract email details
            $subject = $mailable->envelope()->subject ?? 'No Subject';
            $contentPreview = $this->getContentPreview($mailable);

            // Create email log entry
            $emailLog = \App\Models\EmailLog::create([
                'message_id' => $messageId,
                'to_email' => $to,
                'to_name' => $options['to_name'] ?? null,
                'from_email' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
                'subject' => $subject,
                'content_preview' => $contentPreview,
                'provider' => $provider,
                'status' => 'failed',
                'metadata' => [
                    'headers' => $options['headers'] ?? [],
                    'options' => $options,
                ],
                'mailable_class' => get_class($mailable),
                'user_id' => $options['user_id'] ?? null,
                'sent_at' => now(),
                'failed_at' => now(),
                'failure_reason' => $failureReason,
            ]);

            Log::info('Failed email logged manually', [
                'email_log_id' => $emailLog->id,
                'message_id' => $messageId,
                'to_email' => $to,
                'provider' => $provider,
                'failure_reason' => $failureReason,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log failed email', [
                'to' => $to,
                'provider' => $provider,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get content preview from mailable.
     */
    private function getContentPreview($mailable): ?string
    {
        try {
            $content = $this->renderMailable($mailable);
            $htmlContent = $content['html'] ?? '';
            return \Illuminate\Support\Str::limit(strip_tags($htmlContent), 500);
        } catch (\Exception $e) {
            return 'Content preview unavailable';
        }
    }
}
