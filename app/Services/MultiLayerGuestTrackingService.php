<?php

namespace App\Services;

use App\Models\SearchConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MultiLayerGuestTrackingService
{
    public function __construct(
        private IpSearchTrackingService $ipTrackingService,
        private BrowserFingerprintService $fingerprintService,
        private SessionSearchTrackingService $sessionTrackingService
    ) {}

    /**
     * Check search limit using multi-layer approach.
     */
    public function checkSearchLimit(Request $request): array
    {
        $searchLimit = SearchConfiguration::get('guest_search_limit', 3);
        $resetHours = SearchConfiguration::get('guest_search_reset_hours', 24);
        
        // Initialize session tracking
        $this->sessionTrackingService->initializeSession();
        
        // Get tracking data from all layers
        $trackingData = $this->getTrackingLayers($request);
        
        // Check if any layer has exceeded the limit
        $limitExceeded = $this->isLimitExceeded($trackingData, $searchLimit);
        
        if ($limitExceeded) {
            $this->logLimitExceeded($request, $trackingData);
            
            return [
                'error' => 'Search limit exceeded',
                'message' => "You have used all {$searchLimit} of your free searches. Please sign up to continue searching our mobile parts database.",
                'signup_url' => route('register'),
                'login_url' => route('login'),
                'limit_reached' => true,
                'searches_used' => $this->getMaxSearchCount($trackingData),
                'search_limit' => $searchLimit,
                'reset_hours' => $resetHours,
                'tracking_layers' => $this->getTrackingLayerSummary($trackingData),
            ];
        }
        
        // Check for suspicious activity
        $suspiciousActivity = $this->detectSuspiciousActivity($request, $trackingData);
        if (!empty($suspiciousActivity)) {
            $this->handleSuspiciousActivity($request, $suspiciousActivity);
        }
        
        return [
            'can_search' => true,
            'tracking_data' => $trackingData,
            'suspicious_activity' => $suspiciousActivity,
        ];
    }

    /**
     * Increment search count across all tracking layers.
     */
    public function incrementSearchCount(Request $request): void
    {
        $resetHours = SearchConfiguration::get('guest_search_reset_hours', 24);
        $ip = $this->ipTrackingService->getClientIp($request);
        $deviceId = $request->get('device_id', '');
        $fingerprint = $request->get('fingerprint', '');
        
        // Increment IP-based tracking
        $this->ipTrackingService->incrementSearchCount($ip, $resetHours);
        $this->ipTrackingService->trackRapidSearch($ip);
        
        if (!empty($deviceId)) {
            $this->ipTrackingService->trackDeviceUsage($ip, $deviceId);
        }
        
        // Increment fingerprint-based tracking
        if (!empty($fingerprint) && $this->fingerprintService->validateFingerprint($fingerprint)) {
            $this->fingerprintService->incrementSearchCount($fingerprint, $resetHours);
        }
        
        // Increment session-based tracking
        $this->sessionTrackingService->incrementSearchCount($resetHours);
        
        if (!empty($deviceId)) {
            $this->sessionTrackingService->trackDeviceId($deviceId);
        }
        
        Log::info('Multi-layer search count incremented', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'fingerprint_hash' => !empty($fingerprint) ? substr($fingerprint, 0, 8) . '...' : 'none',
            'session_id' => substr(session()->getId(), 0, 8) . '...',
            'device_id_hash' => !empty($deviceId) ? substr(hash('sha256', $deviceId), 0, 8) . '...' : 'none',
        ]);
    }

    /**
     * Get search status from all tracking layers.
     */
    public function getSearchStatus(Request $request): array
    {
        $searchLimit = SearchConfiguration::get('guest_search_limit', 3);
        $resetHours = SearchConfiguration::get('guest_search_reset_hours', 24);
        
        $trackingData = $this->getTrackingLayers($request);
        $maxSearchCount = $this->getMaxSearchCount($trackingData);
        $remainingSearches = max(0, $searchLimit - $maxSearchCount);
        
        return [
            'has_searched' => $maxSearchCount > 0,
            'can_search' => $remainingSearches > 0,
            'searches_used' => $maxSearchCount,
            'search_limit' => $searchLimit,
            'remaining_searches' => $remainingSearches,
            'reset_hours' => $resetHours,
            'tracking_layers' => $this->getTrackingLayerSummary($trackingData),
            'suspicious_activity' => $this->detectSuspiciousActivity($request, $trackingData),
            'message' => $remainingSearches > 0
                ? "You have {$remainingSearches} free searches remaining."
                : "You have used all {$searchLimit} free searches. Sign up for unlimited access.",
        ];
    }

    /**
     * Get tracking data from all layers.
     */
    private function getTrackingLayers(Request $request): array
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        $fingerprint = $request->get('fingerprint', '');
        
        $layers = [
            'ip' => $this->ipTrackingService->getSearchStatus($ip),
            'session' => $this->sessionTrackingService->getSearchStatus(),
        ];
        
        // Add fingerprint layer if available
        if (!empty($fingerprint) && $this->fingerprintService->validateFingerprint($fingerprint)) {
            $layers['fingerprint'] = $this->fingerprintService->getSearchStatus($fingerprint);
        }
        
        return $layers;
    }

    /**
     * Check if limit is exceeded in any layer.
     */
    private function isLimitExceeded(array $trackingData, int $limit): bool
    {
        foreach ($trackingData as $layer => $data) {
            if (($data['searches_used'] ?? 0) >= $limit) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get the maximum search count across all layers.
     */
    private function getMaxSearchCount(array $trackingData): int
    {
        $maxCount = 0;
        
        foreach ($trackingData as $layer => $data) {
            $count = $data['searches_used'] ?? 0;
            if ($count > $maxCount) {
                $maxCount = $count;
            }
        }
        
        return $maxCount;
    }

    /**
     * Get summary of tracking layers for response.
     */
    private function getTrackingLayerSummary(array $trackingData): array
    {
        $summary = [];
        
        foreach ($trackingData as $layer => $data) {
            $summary[$layer] = [
                'searches_used' => $data['searches_used'] ?? 0,
                'can_search' => $data['can_search'] ?? true,
                'remaining_searches' => $data['remaining_searches'] ?? 0,
            ];
        }
        
        return $summary;
    }

    /**
     * Detect suspicious activity across all layers.
     */
    private function detectSuspiciousActivity(Request $request, array $trackingData): array
    {
        $suspiciousActivity = [];
        $ip = $this->ipTrackingService->getClientIp($request);
        
        // Check IP-based suspicious activity
        $ipSuspicious = $this->ipTrackingService->detectSuspiciousActivity($ip);
        if (!empty($ipSuspicious)) {
            $suspiciousActivity['ip'] = $ipSuspicious;
        }
        
        // Check session-based suspicious activity
        $sessionSuspicious = $this->sessionTrackingService->detectSuspiciousActivity();
        if (!empty($sessionSuspicious)) {
            $suspiciousActivity['session'] = $sessionSuspicious;
        }
        
        // Check for inconsistencies between layers
        $inconsistencies = $this->detectLayerInconsistencies($trackingData);
        if (!empty($inconsistencies)) {
            $suspiciousActivity['inconsistencies'] = $inconsistencies;
        }
        
        return $suspiciousActivity;
    }

    /**
     * Detect inconsistencies between tracking layers.
     */
    private function detectLayerInconsistencies(array $trackingData): array
    {
        $inconsistencies = [];
        
        // Get search counts from each layer
        $counts = [];
        foreach ($trackingData as $layer => $data) {
            $counts[$layer] = $data['searches_used'] ?? 0;
        }
        
        // Check for large discrepancies
        $maxCount = max($counts);
        $minCount = min($counts);
        
        if ($maxCount - $minCount > 2) {
            $inconsistencies[] = 'large_count_discrepancy';
        }
        
        // Check for new session with high search count
        if (isset($trackingData['session'], $trackingData['ip'])) {
            $sessionAge = $trackingData['session']['session_age'] ?? 0;
            $ipCount = $trackingData['ip']['searches_used'] ?? 0;
            
            if ($sessionAge < 300 && $ipCount > 1) { // New session but IP has searches
                $inconsistencies[] = 'new_session_with_ip_history';
            }
        }
        
        return $inconsistencies;
    }

    /**
     * Handle suspicious activity.
     */
    private function handleSuspiciousActivity(Request $request, array $suspiciousActivity): void
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        $severity = $this->calculateSuspiciousSeverity($suspiciousActivity);
        
        Log::warning('Suspicious guest search activity detected', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'activity' => $suspiciousActivity,
            'severity' => $severity,
            'user_agent' => $request->userAgent(),
            'timestamp' => now()
        ]);
        
        // Take action based on severity
        if ($severity >= 3) {
            // High severity - block IP temporarily
            $this->ipTrackingService->blockIp($ip, 60);
        } elseif ($severity >= 2) {
            // Medium severity - regenerate session
            $this->sessionTrackingService->regenerateSession();
        }
    }

    /**
     * Calculate severity of suspicious activity.
     */
    private function calculateSuspiciousSeverity(array $suspiciousActivity): int
    {
        $severity = 0;
        
        foreach ($suspiciousActivity as $layer => $activities) {
            if (is_array($activities)) {
                foreach ($activities as $activity) {
                    switch ($activity) {
                        case 'rapid_searches':
                        case 'multiple_devices':
                            $severity += 1;
                            break;
                        case 'proxy_vpn':
                        case 'large_count_discrepancy':
                            $severity += 2;
                            break;
                        case 'rapid_session_usage':
                        case 'new_session_with_ip_history':
                            $severity += 1;
                            break;
                    }
                }
            }
        }
        
        return $severity;
    }

    /**
     * Log limit exceeded event.
     */
    private function logLimitExceeded(Request $request, array $trackingData): void
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        
        Log::info('Guest search limit exceeded', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'tracking_data' => $this->getTrackingLayerSummary($trackingData),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * Clear all tracking data (admin function).
     */
    public function clearAllTrackingData(Request $request): void
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        
        $this->ipTrackingService->clearIpData($ip);
        $this->sessionTrackingService->clearSessionData();
        
        Log::info('All guest tracking data cleared', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'session_id' => substr(session()->getId(), 0, 8) . '...',
            'timestamp' => now()
        ]);
    }
}
