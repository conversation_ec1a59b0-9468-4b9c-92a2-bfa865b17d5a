<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SubscriptionController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Show subscription index page.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        return Inertia::render('subscription/index', [
            'subscription' => $user->activeSubscription,
            'currentPlan' => $user->subscription_plan,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
            'plans' => $this->subscriptionService->getPlans(),
        ]);
    }

    /**
     * Show subscription plans.
     */
    public function plans(Request $request)
    {
        $user = $request->user();

        return Inertia::render('subscription/plans', [
            'plans' => $this->subscriptionService->getPlans(),
            'currentPlan' => $user->subscription_plan,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
        ]);
    }

    /**
     * Show user's subscription dashboard.
     */
    public function dashboard(Request $request)
    {
        $user = $request->user();

        return Inertia::render('subscription/dashboard', [
            'subscription' => $user->activeSubscription,
            'currentPlan' => $user->subscription_plan,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
            'searchHistory' => $user->searches()
                ->latest()
                ->take(10)
                ->get(),
        ]);
    }

    /**
     * Show the checkout page.
     */
    public function showCheckout(Request $request)
    {
        $user = $request->user();

        if ($user->isPremium()) {
            return redirect()->route('subscription.dashboard')
                ->with('error', 'You already have an active premium subscription.');
        }

        $planKey = $request->get('plan', 'premium');
        $plan = $this->subscriptionService->getPricingPlanByName($planKey);

        if (!$plan) {
            return redirect()->route('subscription.plans')
                ->with('error', 'Invalid subscription plan selected.');
        }

        return Inertia::render('subscription/checkout', [
            'plan' => $plan,
            'currentPlan' => $user->subscription_plan,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
        ]);
    }

    /**
     * Create checkout session for premium subscription.
     */
    public function checkout(Request $request)
    {
        $user = $request->user();

        // Validate request
        $validated = $request->validate([
            'plan_id' => 'sometimes|exists:pricing_plans,id',
            'billing_cycle' => 'sometimes|in:month,year',
            'payment_gateway' => 'sometimes|in:paddle,shurjopay,coinbase_commerce,offline'
        ]);

        if ($user->isPremium()) {
            return back()->with('error', 'You already have an active premium subscription.');
        }

        try {
            // Add timeout protection and proper error handling
            set_time_limit(30); // 30 second timeout

            // Create subscription with proper error handling
            $subscription = $this->subscriptionService->createPremiumSubscription(
                $user,
                null,
                $validated['payment_gateway'] ?? 'offline'
            );

            \Log::info('Subscription created successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'plan' => $subscription->plan_name
            ]);

            return redirect()->route('subscription.dashboard')
                ->with('success', 'Premium subscription activated successfully!');

        } catch (\Exception $e) {
            \Log::error('Subscription creation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Failed to create subscription. Please try again or contact support.');
        }
    }

    /**
     * Show payment success page.
     */
    public function success(Request $request)
    {
        $user = $request->user();
        $transactionId = $request->get('transaction_id') ?? $request->get('_ptxn');

        return Inertia::render('subscription/Success', [
            'user' => $user,
            'subscription' => $user->activeSubscription,
            'currentPlan' => $user->subscription_plan,
            'transactionId' => $transactionId,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
        ]);
    }

    /**
     * Show payment cancelled page.
     */
    public function cancelled(Request $request)
    {
        $user = $request->user();
        $reason = $request->get('reason', 'Payment was cancelled');

        return Inertia::render('subscription/Cancelled', [
            'user' => $user,
            'reason' => $reason,
            'plans' => $this->subscriptionService->getPlans(),
            'currentPlan' => $user->subscription_plan,
            'remainingSearches' => $this->subscriptionService->getRemainingSearches($user),
        ]);
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Request $request)
    {
        $user = $request->user();

        if (!$user->isPremium()) {
            return back()->with('error', 'You do not have an active subscription to cancel.');
        }

        $this->subscriptionService->cancelSubscription($user);

        return back()->with('success', 'Subscription cancelled successfully.');
    }

    /**
     * Get user's search statistics.
     */
    public function searchStats(Request $request)
    {
        $user = $request->user();

        // Basic stats for JSON API
        $basicData = [
            'remaining_searches' => $this->subscriptionService->getRemainingSearches($user),
            'is_premium' => $user->isPremium(),
            'current_plan' => $user->subscription_plan,
            'daily_searches' => $user->searches()
                ->whereDate('created_at', today())
                ->count(),
        ];

        // If it's an AJAX request, return JSON
        if ($request->wantsJson()) {
            return response()->json($basicData);
        }

        // For Inertia requests, provide comprehensive analytics data
        $stats = $this->getComprehensiveStats($user);
        $planLimits = [
            'free_daily_limit' => config('app.free_search_limit', 20),
            'premium_daily_limit' => -1, // Unlimited
        ];

        return Inertia::render('subscription/search-stats', [
            'stats' => $stats,
            'plan_limits' => $planLimits,
        ]);
    }

    /**
     * Get comprehensive search statistics for analytics dashboard.
     */
    private function getComprehensiveStats(User $user)
    {
        // Get fresh query instances for each calculation to avoid query conflicts
        $totalSearches = $user->searches()->count();
        $dailySearches = $user->searches()->whereDate('created_at', today())->count();
        $weeklySearches = $user->searches()->where('created_at', '>=', now()->subWeek())->count();
        $monthlySearches = $user->searches()->where('created_at', '>=', now()->subMonth())->count();

        // Success rate calculation
        $successfulSearches = $user->searches()->where('results_count', '>', 0)->count();
        $searchSuccessRate = $totalSearches > 0 ? ($successfulSearches / $totalSearches) * 100 : 0;

        // Most searched terms - use fresh query
        $mostSearchedTerms = $user->searches()
            ->selectRaw('search_query as term, COUNT(*) as search_count, AVG(CASE WHEN results_count > 0 THEN 100 ELSE 0 END) as success_rate')
            ->groupBy('search_query')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'term' => $item->term,
                    'count' => $item->search_count,
                    'success_rate' => round($item->success_rate, 1),
                ];
            });

        // Add default data if no searches exist
        if ($mostSearchedTerms->isEmpty()) {
            $mostSearchedTerms = collect([
                ['term' => 'Start searching to see your top terms', 'count' => 0, 'success_rate' => 0],
            ]);
        }

        // Search types breakdown - use fresh query
        $searchTypesBreakdown = $user->searches()
            ->selectRaw('search_type as type, COUNT(*) as search_count')
            ->groupBy('search_type')
            ->get()
            ->map(function ($item) use ($totalSearches) {
                return [
                    'type' => $item->type,
                    'count' => $item->search_count,
                    'percentage' => $totalSearches > 0 ? round(($item->search_count / $totalSearches) * 100, 1) : 0,
                ];
            });

        // Add default data if no searches exist
        if ($searchTypesBreakdown->isEmpty()) {
            $searchTypesBreakdown = collect([
                ['type' => 'all', 'count' => 0, 'percentage' => 100],
            ]);
        }

        // Daily usage chart (last 30 days) - use individual queries for each day
        $dailyUsageChart = collect(range(29, 0))->map(function ($daysAgo) use ($user) {
            $date = now()->subDays($daysAgo);
            $daySearches = $user->searches()->whereDate('created_at', $date->toDateString())->count();
            $daySuccessful = $user->searches()
                ->whereDate('created_at', $date->toDateString())
                ->where('results_count', '>', 0)
                ->count();

            return [
                'date' => $date->toDateString(),
                'searches' => $daySearches,
                'successful' => $daySuccessful,
            ];
        });

        // Monthly trends (last 12 months) - use individual queries for each month
        $monthlyTrends = collect(range(11, 0))->map(function ($monthsAgo) use ($user) {
            $date = now()->subMonths($monthsAgo);
            $monthSearches = $user->searches()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            return [
                'month' => $date->format('M Y'),
                'searches' => $monthSearches,
                'plan' => $user->subscription_plan,
            ];
        });

        // Popular categories - calculate from actual search data or use defaults
        $popularCategories = $this->getPopularCategories($totalSearches);

        // Usage recommendations
        $usageRecommendations = $this->generateUsageRecommendations($user, $dailySearches, $searchSuccessRate);

        return [
            'remaining_searches' => $this->subscriptionService->getRemainingSearches($user),
            'is_premium' => $user->isPremium(),
            'current_plan' => $user->subscription_plan,
            'daily_searches' => $dailySearches,
            'weekly_searches' => $weeklySearches,
            'monthly_searches' => $monthlySearches,
            'total_searches' => $totalSearches,
            'search_success_rate' => $searchSuccessRate,
            'most_searched_terms' => $mostSearchedTerms,
            'search_types_breakdown' => $searchTypesBreakdown,
            'daily_usage_chart' => $dailyUsageChart,
            'monthly_trends' => $monthlyTrends,
            'popular_categories' => $popularCategories,
            'usage_recommendations' => $usageRecommendations,
        ];
    }

    /**
     * Generate usage recommendations based on user patterns.
     */
    private function generateUsageRecommendations(User $user, int $dailySearches, float $successRate)
    {
        $recommendations = [];

        // Check if user is approaching daily limit
        if (!$user->isPremium()) {
            $freeLimit = config('app.free_search_limit', 20);
            $usagePercentage = ($dailySearches / $freeLimit) * 100;

            if ($usagePercentage >= 80) {
                $recommendations[] = [
                    'type' => 'upgrade',
                    'message' => 'You\'re approaching your daily search limit. Consider upgrading to Premium for unlimited searches.',
                    'action' => 'Upgrade to Premium for unlimited daily searches and advanced features.',
                ];
            } elseif ($usagePercentage >= 50) {
                $recommendations[] = [
                    'type' => 'warning',
                    'message' => 'You\'ve used more than half of your daily searches. Plan accordingly for the rest of the day.',
                ];
            }
        }

        // Success rate recommendations
        if ($successRate < 50) {
            $recommendations[] = [
                'type' => 'tip',
                'message' => 'Your search success rate is below 50%. Try using more specific part numbers or model names.',
                'action' => 'Use exact part numbers when available for better results.',
            ];
        }

        // Usage pattern recommendations
        if ($dailySearches > 15 && !$user->isPremium()) {
            $recommendations[] = [
                'type' => 'upgrade',
                'message' => 'You\'re a power user! Upgrade to Premium to unlock unlimited searches and advanced features.',
                'action' => 'Get unlimited searches, high-resolution images, and priority support.',
            ];
        }

        return $recommendations;
    }

    /**
     * Get popular categories from search data.
     */
    private function getPopularCategories(int $totalSearches)
    {
        // For now, return mock data since we don't have category tracking in searches
        // In a real implementation, you'd join with parts and categories tables
        return [
            ['category' => 'Display', 'searches' => max(1, intval($totalSearches * 0.35)), 'percentage' => 35],
            ['category' => 'Battery', 'searches' => max(1, intval($totalSearches * 0.25)), 'percentage' => 25],
            ['category' => 'Camera', 'searches' => max(1, intval($totalSearches * 0.20)), 'percentage' => 20],
            ['category' => 'IC Components', 'searches' => max(1, intval($totalSearches * 0.12)), 'percentage' => 12],
            ['category' => 'Speaker', 'searches' => max(1, intval($totalSearches * 0.08)), 'percentage' => 8],
        ];
    }
}
