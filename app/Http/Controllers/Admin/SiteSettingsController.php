<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SiteSettingsController extends Controller
{
    /**
     * Display the site settings management page.
     */
    public function index()
    {
        $settings = SiteSetting::where('is_active', true)
            ->orderBy('category')
            ->orderBy('key')
            ->get()
            ->groupBy('category');

        // Ensure all default categories exist
        $defaults = SiteSetting::getDefaults();
        $defaultsByCategory = collect($defaults)->groupBy(function ($item) {
            return $item['category'];
        });

        foreach ($defaultsByCategory as $category => $categoryDefaults) {
            if (!$settings->has($category)) {
                $settings[$category] = collect();
            }
        }

        // Get active menus for navbar and footer selection
        $menus = \App\Models\Menu::active()
            ->select('id', 'name', 'location', 'description')
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/SiteSettings/Index', [
            'settings' => $settings,
            'categories' => $settings->keys(),
            'menus' => $menus,
        ]);
    }

    /**
     * Update site settings.
     */
    public function update(Request $request)
    {
        // Handle both direct form data and nested settings array
        $settingsData = $request->has('settings') ? $request->settings : $request->all();

        // If it's direct form data, convert to settings array format
        if (!$request->has('settings')) {
            $settingsArray = [];
            $defaults = SiteSetting::getDefaults();

            foreach ($settingsData as $key => $value) {
                if (isset($defaults[$key])) {
                    $settingsArray[] = [
                        'key' => $key,
                        'value' => $value,
                        'type' => $defaults[$key]['type']
                    ];
                }
            }
            $settingsData = $settingsArray;
        }

        $validator = Validator::make(['settings' => $settingsData], [
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
            'settings.*.type' => 'required|string|in:string,boolean,integer,array,json',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            foreach ($settingsData as $settingData) {
                $key = $settingData['key'];
                $value = $settingData['value'];
                $type = $settingData['type'];

                // Type casting based on setting type
                switch ($type) {
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'array':
                    case 'json':
                        if (is_string($value)) {
                            $value = json_decode($value, true) ?? [];
                        }
                        break;
                    default:
                        $value = (string) $value;
                }

                $setting = SiteSetting::where('key', $key)->first();

                if ($setting) {
                    $setting->update(['value' => $value]);
                } else {
                    // Create new setting with defaults
                    $defaults = SiteSetting::getDefaults();
                    $defaultConfig = $defaults[$key] ?? [];

                    SiteSetting::create([
                        'key' => $key,
                        'value' => $value,
                        'type' => $type,
                        'description' => $defaultConfig['description'] ?? '',
                        'category' => $defaultConfig['category'] ?? 'general',
                        'is_active' => true,
                    ]);
                }
            }

            return back()->with('success', 'Site settings updated successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update site settings: ' . $e->getMessage());
        }
    }

    /**
     * Reset settings to defaults.
     */
    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $category = $request->category;
            
            if ($category) {
                // Reset specific category
                SiteSetting::where('category', $category)->delete();
                
                $defaults = SiteSetting::getDefaults();
                foreach ($defaults as $key => $config) {
                    if ($config['category'] === $category) {
                        SiteSetting::updateOrCreate(
                            ['key' => $key],
                            [
                                'value' => $config['value'],
                                'type' => $config['type'],
                                'description' => $config['description'],
                                'category' => $config['category'],
                                'is_active' => true,
                            ]
                        );
                    }
                }
                
                $message = "Settings for '{$category}' category reset to defaults.";
            } else {
                // Reset all settings
                SiteSetting::truncate();
                SiteSetting::seedDefaults();
                $message = 'All site settings reset to defaults.';
            }

            return back()->with('success', $message);
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to reset settings: ' . $e->getMessage());
        }
    }

    /**
     * Get settings for API consumption.
     */
    public function api()
    {
        $settings = SiteSetting::where('is_active', true)
            ->pluck('value', 'key');

        return response()->json($settings);
    }

    /**
     * Get settings by category for API consumption.
     */
    public function apiByCategory(string $category)
    {
        $settings = SiteSetting::getByCategory($category);

        return response()->json($settings);
    }

    /**
     * Get public branding settings (accessible without admin privileges).
     */
    public function publicBranding()
    {
        $brandingSettings = SiteSetting::where('is_active', true)
            ->where('category', 'branding')
            ->pluck('value', 'key');

        return response()->json($brandingSettings);
    }

    /**
     * Get footer configuration for public consumption.
     */
    public function footerConfig()
    {
        $footerSettings = SiteSetting::getByCategory('footer');

        // Ensure all required settings exist with defaults
        $defaults = SiteSetting::getDefaults();
        foreach ($defaults as $key => $config) {
            if ($config['category'] === 'footer' && !isset($footerSettings[$key])) {
                $footerSettings[$key] = $config['value'];
            }
        }

        // If footer has menus selected, include menu items
        if (!empty($footerSettings['footer_menu_ids']) && is_array($footerSettings['footer_menu_ids'])) {
            $menus = \App\Models\Menu::with(['rootItems.children' => function ($query) {
                $query->where('is_active', true)->orderBy('order');
            }])->whereIn('id', $footerSettings['footer_menu_ids'])
              ->where('is_active', true)
              ->get();

            $footerSettings['footer_menus'] = $menus;
        } else {
            $footerSettings['footer_menus'] = [];
        }

        return response()->json($footerSettings);
    }

    /**
     * Get navbar configuration for public consumption.
     */
    public function navbarConfig()
    {
        $navbarSettings = SiteSetting::getByCategory('navbar');

        // Ensure all required settings exist with defaults
        $defaults = SiteSetting::getDefaults();
        foreach ($defaults as $key => $config) {
            if ($config['category'] === 'navbar' && !isset($navbarSettings[$key])) {
                $navbarSettings[$key] = $config['value'];
            }
        }

        // If navbar has a menu selected, include menu items
        if (!empty($navbarSettings['navbar_menu_id'])) {
            $menu = \App\Models\Menu::with(['rootItems.children' => function ($query) {
                $query->where('is_active', true)->orderBy('order');
            }])->find($navbarSettings['navbar_menu_id']);

            $navbarSettings['menu_items'] = $menu ? $menu->rootItems : [];
        } else {
            $navbarSettings['menu_items'] = [];
        }

        return response()->json($navbarSettings);
    }
}
