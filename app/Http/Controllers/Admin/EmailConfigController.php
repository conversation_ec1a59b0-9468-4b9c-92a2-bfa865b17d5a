<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\UserApproved;
use App\Mail\TestEmail;
use App\Models\User;
use App\Services\EmailService;
use App\Services\EmailTrackingService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Inertia\Response;

class EmailConfigController extends Controller
{
    public function __construct(
        private EmailService $emailService,
        private EmailTrackingService $trackingService
    ) {
        //
    }

    /**
     * Display email configuration dashboard.
     */
    public function index(): Response
    {
        try {
            $currentConfig = [
                'default_provider' => config('mail.default'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
                'smtp' => [
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'username' => config('mail.mailers.smtp.username'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'password_set' => !empty(config('mail.mailers.smtp.password')),
                ],
                'sendgrid' => [
                    'api_key_set' => !empty(config('services.sendgrid.api_key')),
                    'api_key_prefix' => config('services.sendgrid.api_key')
                        ? substr(config('services.sendgrid.api_key'), 0, 10) . '...'
                        : null,
                ],
            ];

            $providerStatus = $this->emailService->getProviderStatus();
            $emailStats = $this->emailService->getEmailStatistics();

            return Inertia::render('admin/EmailConfig/Index', [
                'config' => $currentConfig,
                'provider_status' => $providerStatus,
                'email_stats' => $emailStats,
                'error_details' => session('error_details'),
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Email configuration page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Return with default/fallback data to prevent crashes
            $fallbackConfig = [
                'default_provider' => 'log',
                'from_address' => config('mail.from.address', '<EMAIL>'),
                'from_name' => config('mail.from.name', 'Application'),
                'smtp' => [
                    'host' => '',
                    'port' => 587,
                    'username' => '',
                    'encryption' => 'tls',
                    'password_set' => false,
                ],
                'sendgrid' => [
                    'api_key_set' => false,
                    'api_key_prefix' => null,
                ],
            ];

            $fallbackProviderStatus = [
                'provider' => 'log',
                'configured' => false,
                'status' => 'error'
            ];

            $fallbackEmailStats = [
                'total_sent' => 0,
                'total_delivered' => 0,
                'total_bounced' => 0,
                'total_opened' => 0,
                'total_clicked' => 0,
                'delivery_rate' => 0,
                'open_rate' => 0,
                'click_rate' => 0,
                'bounce_rate' => 0,
                'provider' => 'log',
                'period_days' => 30
            ];

            return Inertia::render('admin/EmailConfig/Index', [
                'config' => $fallbackConfig,
                'provider_status' => $fallbackProviderStatus,
                'email_stats' => $fallbackEmailStats,
                'error' => 'There was an issue loading email configuration. Please check the logs for details.',
            ]);
        }
    }

    /**
     * Update email configuration.
     */
    public function updateConfig(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'provider' => 'required|in:smtp,sendgrid',
            'from_address' => 'required|email',
            'from_name' => 'required|string|max:255',
            'smtp_host' => 'required_if:provider,smtp|nullable|string',
            'smtp_port' => 'required_if:provider,smtp|nullable|integer|min:1|max:65535',
            'smtp_username' => 'required_if:provider,smtp|nullable|string',
            'smtp_password' => 'nullable|string',
            'smtp_encryption' => 'required_if:provider,smtp|nullable|in:tls,ssl,null',
            'sendgrid_api_key' => 'required_if:provider,sendgrid|nullable|string',
        ]);

        try {
            // Update environment variables
            $this->updateEnvFile([
                'MAIL_MAILER' => $validated['provider'],
                'MAIL_FROM_ADDRESS' => $validated['from_address'],
                'MAIL_FROM_NAME' => $validated['from_name'],
            ]);

            if ($validated['provider'] === 'smtp') {
                $this->updateEnvFile([
                    'MAIL_HOST' => $validated['smtp_host'],
                    'MAIL_PORT' => $validated['smtp_port'],
                    'MAIL_USERNAME' => $validated['smtp_username'],
                    'MAIL_ENCRYPTION' => $validated['smtp_encryption'] ?: 'null',
                ]);

                if (!empty($validated['smtp_password'])) {
                    $this->updateEnvFile(['MAIL_PASSWORD' => $validated['smtp_password']]);
                }
            } elseif ($validated['provider'] === 'sendgrid') {
                $this->updateEnvFile([
                    'SENDGRID_API_KEY' => $validated['sendgrid_api_key'],
                ]);
            }

            // Clear config cache
            Artisan::call('config:clear');

            return redirect()->back()
                ->with('success', 'Email configuration updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update email configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test email configuration.
     */
    public function testConfig(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'provider' => 'nullable|in:smtp,sendgrid',
        ]);

        $provider = $validated['provider'] ?? config('mail.default');
        $result = $this->emailService->testConfiguration($provider);

        if ($result['success']) {
            return redirect()->back()
                ->with('success', $result['message']);
        } else {
            $errorData = [
                'message' => $result['message'],
                'troubleshooting' => $result['troubleshooting'] ?? null,
                'details' => $result['details'] ?? null
            ];

            return redirect()->back()
                ->with('error', $result['message'])
                ->with('error_details', $errorData);
        }
    }

    /**
     * Send test email.
     */
    public function sendTestEmail(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        try {
            // Send test email using dedicated TestEmail mailable (not queued)
            $success = $this->emailService->send(
                new TestEmail('This is a test email sent from the admin panel to verify your email configuration is working correctly.'),
                $validated['email']
            );

            if ($success) {
                return redirect()->back()
                    ->with('success', 'Test email sent successfully to ' . $validated['email']);
            } else {
                return redirect()->back()
                    ->with('error', 'Failed to send test email. Check logs for details.');
            }

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to send test email: ' . $e->getMessage());
        }
    }

    /**
     * Get email statistics.
     */
    public function getStats(Request $request)
    {
        $days = $request->get('days', 30);
        $stats = $this->emailService->getEmailStatistics($days);

        return response()->json($stats);
    }

    /**
     * Update environment file.
     */
    private function updateEnvFile(array $data): void
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}=" . $this->formatEnvValue($value);

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }

    /**
     * Format environment variable value for .env file.
     */
    private function formatEnvValue($value): string
    {
        if ($value === null || $value === '') {
            return '';
        }

        // Convert to string
        $value = (string) $value;

        // If value already has quotes, return as is
        if ((str_starts_with($value, '"') && str_ends_with($value, '"')) ||
            (str_starts_with($value, "'") && str_ends_with($value, "'"))) {
            return $value;
        }

        // If value contains spaces, special characters, or quotes, wrap in double quotes
        if (preg_match('/[\s"\'#$&*(){}[\]|\\\\;`~<>?]/', $value)) {
            // Escape any existing double quotes
            $value = str_replace('"', '\\"', $value);
            return '"' . $value . '"';
        }

        return $value;
    }

    /**
     * Get email provider documentation.
     */
    public function getProviderDocs(string $provider)
    {
        $docs = [
            'smtp' => [
                'title' => 'SMTP Configuration',
                'description' => 'Configure SMTP settings for sending emails through your email provider.',
                'fields' => [
                    'host' => 'SMTP server hostname (e.g., smtp.gmail.com)',
                    'port' => 'SMTP server port (usually 587 for TLS, 465 for SSL)',
                    'username' => 'Your email address or SMTP username',
                    'password' => 'Your email password or app-specific password',
                    'encryption' => 'Encryption method (TLS recommended)',
                ],
                'examples' => [
                    'Gmail' => [
                        'host' => 'smtp.gmail.com',
                        'port' => 587,
                        'encryption' => 'tls',
                        'note' => 'Use app-specific password for 2FA accounts'
                    ],
                    'Outlook' => [
                        'host' => 'smtp-mail.outlook.com',
                        'port' => 587,
                        'encryption' => 'tls',
                    ],
                    'Yahoo' => [
                        'host' => 'smtp.mail.yahoo.com',
                        'port' => 587,
                        'encryption' => 'tls',
                    ],
                ],
            ],
            'sendgrid' => [
                'title' => 'SendGrid Configuration',
                'description' => 'Configure SendGrid API for reliable email delivery with advanced features.',
                'fields' => [
                    'api_key' => 'SendGrid API key (starts with SG.)',
                ],
                'setup_steps' => [
                    '1. Create a SendGrid account at https://sendgrid.com',
                    '2. Go to Settings > API Keys',
                    '3. Create a new API key with "Full Access" permissions',
                    '4. Copy the API key and paste it in the configuration',
                ],
                'features' => [
                    'High deliverability rates',
                    'Email analytics and tracking',
                    'Template management',
                    'Bounce and spam handling',
                    'Scalable infrastructure',
                ],
            ],
        ];

        return response()->json($docs[$provider] ?? []);
    }

    /**
     * Simulate tracking events for testing purposes.
     */
    public function simulateTrackingEvents(): \Illuminate\Http\JsonResponse
    {
        try {
            $results = $this->trackingService->createSampleTrackingEvents();

            return response()->json([
                'success' => true,
                'message' => 'Sample tracking events created successfully',
                'results' => $results,
                'statistics' => $this->trackingService->getTrackingStatistics(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to simulate tracking events', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to simulate tracking events: ' . $e->getMessage(),
            ], 500);
        }
    }
}
