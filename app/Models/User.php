<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'paddle_customer_id',
        'coinbase_commerce_customer_id',
        'subscription_plan',
        'subscription_status',
        'subscription_ends_at',
        'search_count',
        'daily_reset',
        'status',
        'approval_status',
        'approved_by',
        'approved_at',
        'suspended_at',
        'suspended_by',
        'suspension_reason',
        'suspension_expires_at',
        'last_login_at',
        'login_count',
        'two_factor_enabled',
        'two_factor_confirmed_at',
        'current_otp_code',
        'otp_expires_at',
        'otp_attempts',
        'otp_last_attempt_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'daily_reset' => 'date',
            'subscription_ends_at' => 'datetime',
            'approved_at' => 'datetime',
            'suspended_at' => 'datetime',
            'suspension_expires_at' => 'datetime',
            'last_login_at' => 'datetime',
            'two_factor_enabled' => 'boolean',
            'two_factor_confirmed_at' => 'datetime',
            'otp_expires_at' => 'datetime',
            'otp_last_attempt_at' => 'datetime',
        ];
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')->with('pricingPlan');
    }

    /**
     * Get the user's search history.
     */
    public function searches(): HasMany
    {
        return $this->hasMany(UserSearch::class);
    }

    /**
     * Get the user's favorites.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(UserFavorite::class);
    }

    /**
     * Get the user's payment requests.
     */
    public function paymentRequests(): HasMany
    {
        return $this->hasMany(PaymentRequest::class);
    }

    /**
     * Get the user's activity logs.
     */
    public function activityLogs(): HasMany
    {
        return $this->hasMany(UserActivityLog::class);
    }

    /**
     * Get the user's notifications.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(UserNotification::class);
    }

    /**
     * Get the user's Paddle transactions.
     */
    public function paddleTransactions(): HasMany
    {
        return $this->hasMany(PaddleTransaction::class);
    }

    /**
     * Get the user's Coinbase Commerce transactions.
     */
    public function coinbaseCommerceTransactions(): HasMany
    {
        return $this->hasMany(CoinbaseCommerceTransaction::class);
    }

    /**
     * Get the admin who approved this user.
     */
    public function approvedBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the admin who suspended this user.
     */
    public function suspendedBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'suspended_by');
    }

    /**
     * Get impersonation logs where this user was the admin.
     */
    public function impersonationLogsAsAdmin(): HasMany
    {
        return $this->hasMany(UserImpersonationLog::class, 'admin_user_id');
    }

    /**
     * Get impersonation logs where this user was the target.
     */
    public function impersonationLogsAsTarget(): HasMany
    {
        return $this->hasMany(UserImpersonationLog::class, 'target_user_id');
    }

    /**
     * Check if user has premium subscription.
     */
    public function isPremium(): bool
    {
        if ($this->subscription_plan !== 'premium') {
            return false;
        }

        $activeSubscription = $this->activeSubscription;
        return $activeSubscription && $activeSubscription->isActive();
    }

    /**
     * Check if user can perform search (within daily limit).
     */
    public function canSearch(): bool
    {
        // Admin users have unlimited search access
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isPremium()) {
            return true;
        }

        // Reset daily count if needed
        if ($this->daily_reset < now()->toDateString()) {
            $this->update([
                'search_count' => 0,
                'daily_reset' => now()->toDateString(),
            ]);
        }

        return $this->search_count < 20; // Free tier limit
    }

    /**
     * Increment search count.
     */
    public function incrementSearchCount(): void
    {
        // Don't increment search count for admin users or premium users
        if (!$this->isAdmin() && !$this->isPremium()) {
            $this->increment('search_count');
        }
    }

    /**
     * Get user's remaining searches for today.
     */
    public function getRemainingSearches(): int
    {
        // Admin users have unlimited searches
        if ($this->isAdmin()) {
            return -1; // Unlimited
        }

        if ($this->isPremium()) {
            return -1; // Unlimited
        }

        $limit = config('app.free_search_limit', 20);

        // Reset daily count if needed
        if ($this->daily_reset < now()->toDateString()) {
            $this->update([
                'search_count' => 0,
                'daily_reset' => now()->toDateString(),
            ]);
            return $limit;
        }

        return max(0, $limit - $this->search_count);
    }

    public function isAdmin(): bool
    {
        $adminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        return in_array($this->email, $adminEmails);
    }

    /**
     * Check if user is active and can access the system.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->approval_status === 'approved';
    }

    /**
     * Check if user is suspended.
     */
    public function isSuspended(): bool
    {
        if ($this->status !== 'suspended') {
            return false;
        }

        // Check if temporary suspension has expired
        if ($this->suspension_expires_at && $this->suspension_expires_at->isPast()) {
            $this->update([
                'status' => 'active',
                'suspended_at' => null,
                'suspended_by' => null,
                'suspension_reason' => null,
                'suspension_expires_at' => null,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Check if user is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Approve the user.
     */
    public function approve(User $admin): void
    {
        $this->update([
            'approval_status' => 'approved',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'status' => 'active',
        ]);
    }

    /**
     * Suspend the user.
     */
    public function suspend(User $admin, string $reason, ?\Carbon\Carbon $expiresAt = null): void
    {
        $this->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspended_by' => $admin->id,
            'suspension_reason' => $reason,
            'suspension_expires_at' => $expiresAt,
        ]);
    }

    /**
     * Unsuspend the user.
     */
    public function unsuspend(): void
    {
        $this->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspended_by' => null,
            'suspension_reason' => null,
            'suspension_expires_at' => null,
        ]);
    }

    /**
     * Log user activity.
     */
    public function logActivity(string $activityType, string $description, array $metadata = [], ?User $performedBy = null): void
    {
        $this->activityLogs()->create([
            'activity_type' => $activityType,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => $metadata,
            'performed_by' => $performedBy?->id,
        ]);
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(): void
    {
        $this->update([
            'last_login_at' => now(),
            'login_count' => $this->login_count + 1,
        ]);
    }


}
