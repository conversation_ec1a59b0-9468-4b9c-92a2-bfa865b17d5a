<?php

namespace App\Notifications;

use App\Services\EmailService;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomResetPassword extends ResetPassword implements ShouldQueue
{
    use Queueable;

    /**
     * The password reset token.
     */
    public $token;

    /**
     * The callback that should be used to create the reset password URL.
     */
    public static $createUrlCallback;

    /**
     * The callback that should be used to build the mail message.
     */
    public static $toMailCallback;

    /**
     * Create a notification instance.
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        if (static::$toMailCallback) {
            $mailMessage = call_user_func(static::$toMailCallback, $notifiable, $this->token);
        } else {
            $mailMessage = $this->buildMailMessage($this->resetUrl($notifiable));
        }

        // Use EmailService to send with tracking
        $emailService = app(EmailService::class);
        $mailable = new \App\Mail\CustomPasswordResetMail($mailMessage, $notifiable);

        try {
            $emailService->send($mailable, $notifiable->getEmailForPasswordReset(), [
                'user_id' => $notifiable->getKey(),
                'to_name' => $notifiable->name ?? $notifiable->getEmailForPasswordReset(),
            ]);
        } catch (\Exception $e) {
            // Fall back to default Laravel notification system if EmailService fails
            return $mailMessage;
        }

        // Return empty mail message to prevent double sending
        return (new MailMessage)->line('Email sent via EmailService');
    }

    /**
     * Get the reset password URL for the given notifiable.
     */
    protected function resetUrl($notifiable): string
    {
        if (static::$createUrlCallback) {
            return call_user_func(static::$createUrlCallback, $notifiable, $this->token);
        }

        return url(route('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));
    }

    /**
     * Build the mail message.
     */
    protected function buildMailMessage($url): MailMessage
    {
        return (new MailMessage)
            ->subject('Reset Password Notification')
            ->line('You are receiving this email because we received a password reset request for your account.')
            ->action('Reset Password', $url)
            ->line('This password reset link will expire in '.config('auth.passwords.'.config('auth.defaults.passwords').'.expire').' minutes.')
            ->line('If you did not request a password reset, no further action is required.');
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }
}
