<?php

namespace App\Notifications;

use App\Services\EmailService;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class CustomVerifyEmail extends VerifyEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $verificationUrl = $this->verificationUrl($notifiable);

        return $this->buildMailMessage($verificationUrl);
    }

    /**
     * Build the mail message.
     */
    protected function buildMailMessage($url): MailMessage
    {
        return (new MailMessage)
            ->subject('Verify Email Address')
            ->line('Please click the button below to verify your email address.')
            ->action('Verify Email Address', $url)
            ->line('If you did not create an account, no further action is required.');
    }

    /**
     * Get the verification URL for the given notifiable.
     */
    protected function verificationUrl($notifiable): string
    {
        return URL::temporarySignedRoute(
            'verification.verify',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60)),
            [
                'id' => $notifiable->getKey(),
                'hash' => sha1($notifiable->getEmailForVerification()),
            ]
        );
    }

    /**
     * Send the notification using EmailService for tracking.
     */
    public function send($notifiable, Notification $notification)
    {
        $emailService = app(EmailService::class);
        
        // Create a mailable from the mail message
        $mailMessage = $this->toMail($notifiable);
        $mailable = new \App\Mail\CustomVerificationMail($mailMessage, $notifiable);
        
        try {
            $emailService->send($mailable, $notifiable->getEmailForVerification(), [
                'user_id' => $notifiable->getKey(),
                'to_name' => $notifiable->name ?? $notifiable->getEmailForVerification(),
            ]);
        } catch (\Exception $e) {
            // Fall back to default Laravel notification system if EmailService fails
            parent::send($notifiable, $notification);
        }
    }
}
