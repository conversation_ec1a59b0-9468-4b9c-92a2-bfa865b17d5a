<?php

namespace App\Notifications;

use App\Services\EmailService;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class CustomVerifyEmail extends VerifyEmail implements ShouldQueue
{
    use Queueable;



    /**
     * Build the mail message.
     */
    protected function buildMailMessage($url): MailMessage
    {
        return (new MailMessage)
            ->subject('Verify Email Address')
            ->line('Please click the button below to verify your email address.')
            ->action('Verify Email Address', $url)
            ->line('If you did not create an account, no further action is required.');
    }

    /**
     * Get the verification URL for the given notifiable.
     */
    protected function verificationUrl($notifiable): string
    {
        return URL::temporarySignedRoute(
            'verification.verify',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60)),
            [
                'id' => $notifiable->getKey(),
                'hash' => sha1($notifiable->getEmailForVerification()),
            ]
        );
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Send the notification using EmailService for tracking.
     */
    public function toMail($notifiable)
    {
        $verificationUrl = $this->verificationUrl($notifiable);
        $mailMessage = $this->buildMailMessage($verificationUrl);

        // Use EmailService to send with tracking
        $emailService = app(EmailService::class);
        $mailable = new \App\Mail\CustomVerificationMail($mailMessage, $notifiable);

        try {
            $emailService->send($mailable, $notifiable->getEmailForVerification(), [
                'user_id' => $notifiable->getKey(),
                'to_name' => $notifiable->name ?? $notifiable->getEmailForVerification(),
            ]);
        } catch (\Exception $e) {
            // Fall back to default Laravel notification system if EmailService fails
            return $mailMessage;
        }

        // Return empty mail message to prevent double sending
        return (new MailMessage)->line('Email sent via EmailService');
    }
}
