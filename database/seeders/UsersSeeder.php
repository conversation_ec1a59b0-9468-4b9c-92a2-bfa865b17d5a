<?php

namespace Database\Seeders;

use App\Models\Part;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserFavorite;
use App\Models\UserSearch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createAdminUsers();
        $this->createTestUsers();
        $this->createPremiumUsers();
        $this->createFreeUsers();
        $this->createUserSearchHistory();

        // Only create user favorites if parts exist and we have users without favorites
        if ($this->shouldCreateUserFavorites()) {
            $this->createUserFavorites();
        } else {
            $this->command->info('⚠ Skipped user favorites creation (no parts available or favorites already exist)');
        }
    }

    /**
     * Check if we should create user favorites.
     */
    private function shouldCreateUserFavorites(): bool
    {
        // Check if parts exist
        $partsCount = Part::count();
        if ($partsCount === 0) {
            return false;
        }

        // Check if user favorites already exist
        $favoritesCount = UserFavorite::count();
        if ($favoritesCount > 0) {
            return false;
        }

        // Check if we have users
        $usersCount = User::count();
        if ($usersCount === 0) {
            return false;
        }

        return true;
    }

    /**
     * Create admin users for testing.
     */
    private function createAdminUsers(): void
    {
        // Super Admin
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Nezam Uddin',
                'email_verified_at' => now(),
                'password' => Hash::make('R1451212'),
                'subscription_plan' => 'premium',
                'search_count' => 0,
                'daily_reset' => today(),
                'status' => 'active',
                'approval_status' => 'approved',
                'approved_at' => now(),
            ]
        );

        // Create premium subscription for admin if it doesn't exist
        if (!$superAdmin->subscriptions()->where('plan_name', 'premium')->where('status', 'active')->exists()) {
            Subscription::create([
                'user_id' => $superAdmin->id,
                'plan_name' => 'premium',
                'status' => 'active',
                'current_period_start' => now(),
                'current_period_end' => now()->addYear(),
            ]);
        }

        // Second Admin
        $secondAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Firoz Anam',
                'email_verified_at' => now(),
                'password' => Hash::make('Interstellar@@#1940'),
                'subscription_plan' => 'premium',
                'search_count' => 0,
                'daily_reset' => today(),
                'status' => 'active',
                'approval_status' => 'approved',
                'approved_at' => now(),
            ]
        );

        // Create premium subscription for second admin if it doesn't exist
        if (!$secondAdmin->subscriptions()->where('plan_name', 'premium')->where('status', 'active')->exists()) {
            Subscription::create([
                'user_id' => $secondAdmin->id,
                'plan_name' => 'premium',
                'status' => 'active',
                'current_period_start' => now(),
                'current_period_end' => now()->addYear(),
            ]);
        }

        // Content Manager
        $contentManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Content Manager',
                'email_verified_at' => now(),
                'password' => Hash::make('content123'),
                'subscription_plan' => 'premium',
                'search_count' => 0,
                'daily_reset' => today(),
                'status' => 'active',
                'approval_status' => 'approved',
                'approved_at' => now(),
            ]
        );

        // Create premium subscription for content manager if it doesn't exist
        if (!$contentManager->subscriptions()->where('plan_name', 'premium')->where('status', 'active')->exists()) {
            Subscription::create([
                'user_id' => $contentManager->id,
                'plan_name' => 'premium',
                'status' => 'active',
                'current_period_start' => now(),
                'current_period_end' => now()->addYear(),
            ]);
        }

        $this->command->info('✓ Created admin users');
    }

    /**
     * Create test users with known credentials.
     */
    private function createTestUsers(): void
    {
        // Premium Test User
        $premiumUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Premium Test User',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'premium',
                'search_count' => 45,
                'daily_reset' => today(),
            ]
        );

        // Create subscription if it doesn't exist
        if (!$premiumUser->subscriptions()->where('plan_name', 'premium')->where('status', 'active')->exists()) {
            Subscription::create([
                'user_id' => $premiumUser->id,
                'plan_name' => 'premium',
                'status' => 'active',
                'current_period_start' => now()->subDays(15),
                'current_period_end' => now()->addDays(15),
            ]);
        }

        // Free Test User
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Free Test User',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => 15,
                'daily_reset' => today(),
            ]
        );

        // Free User Near Limit
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Free User Near Limit',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => 19,
                'daily_reset' => today(),
            ]
        );

        // Free User Over Limit
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Free User Over Limit',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => 25,
                'daily_reset' => today(),
            ]
        );

        $this->command->info('✓ Created test users with known credentials');
    }

    /**
     * Create premium users with various subscription states.
     */
    private function createPremiumUsers(): void
    {
        // Skip if premium users already exist
        if (User::where('email', 'like', '<EMAIL>')->exists()) {
            $this->command->info('⚠ Premium users already exist, skipping creation');
            return;
        }

        // Active Premium Users
        for ($i = 1; $i <= 5; $i++) {
            $user = User::create([
                'name' => "Premium User {$i}",
                'email' => "premium{$i}@example.com",
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'premium',
                'search_count' => fake()->numberBetween(50, 200),
                'daily_reset' => today(),
            ]);

            Subscription::create([
                'user_id' => $user->id,
                'plan_name' => 'premium',
                'status' => 'active',
                'current_period_start' => now()->subDays(fake()->numberBetween(1, 25)),
                'current_period_end' => now()->addDays(fake()->numberBetween(5, 30)),
            ]);
        }

        // Cancelled Premium User
        $cancelledUser = User::create([
            'name' => 'Cancelled Premium User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'subscription_plan' => 'free', // Downgraded
            'search_count' => 5,
            'daily_reset' => today(),
        ]);

        Subscription::create([
            'user_id' => $cancelledUser->id,
            'plan_name' => 'premium',
            'status' => 'cancelled',
            'current_period_start' => now()->subDays(30),
            'current_period_end' => now()->subDays(5),
        ]);

        // Expired Premium User
        $expiredUser = User::create([
            'name' => 'Expired Premium User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'subscription_plan' => 'free', // Downgraded
            'search_count' => 12,
            'daily_reset' => today(),
        ]);

        Subscription::create([
            'user_id' => $expiredUser->id,
            'plan_name' => 'premium',
            'status' => 'expired',
            'current_period_start' => now()->subDays(60),
            'current_period_end' => now()->subDays(30),
        ]);

        $this->command->info('✓ Created premium users with various subscription states');
    }

    /**
     * Create free users with different usage patterns.
     */
    private function createFreeUsers(): void
    {
        // Skip if free users already exist
        if (User::where('email', 'like', '<EMAIL>')->exists()) {
            $this->command->info('⚠ Free users already exist, skipping creation');
            return;
        }

        // New Free Users (low usage)
        for ($i = 1; $i <= 10; $i++) {
            User::create([
                'name' => "New User {$i}",
                'email' => "newuser{$i}@example.com",
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => fake()->numberBetween(0, 5),
                'daily_reset' => today(),
            ]);
        }

        // Active Free Users (moderate usage)
        for ($i = 1; $i <= 15; $i++) {
            User::create([
                'name' => "Active Free User {$i}",
                'email' => "activefree{$i}@example.com",
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => fake()->numberBetween(6, 15),
                'daily_reset' => today(),
            ]);
        }

        // Heavy Free Users (near/at limit)
        for ($i = 1; $i <= 8; $i++) {
            User::create([
                'name' => "Heavy Free User {$i}",
                'email' => "heavyfree{$i}@example.com",
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => fake()->numberBetween(16, 20),
                'daily_reset' => today(),
            ]);
        }

        // Users with old daily reset (should be reset)
        for ($i = 1; $i <= 5; $i++) {
            User::create([
                'name' => "Old Reset User {$i}",
                'email' => "oldreset{$i}@example.com",
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'subscription_plan' => 'free',
                'search_count' => fake()->numberBetween(15, 25),
                'daily_reset' => now()->subDays(fake()->numberBetween(1, 3)),
            ]);
        }

        $this->command->info('✓ Created free users with different usage patterns');
    }

    /**
     * Create search history for users.
     */
    private function createUserSearchHistory(): void
    {
        // Skip if search history already exists
        if (UserSearch::count() > 0) {
            $this->command->info('⚠ User search history already exists, skipping creation');
            return;
        }

        $users = User::all();
        $searchQueries = [
            'iPhone 15 display',
            'Samsung Galaxy S24 battery',
            'iPhone 14 camera',
            'Xiaomi Mi 13 screen',
            'OnePlus charging port',
            'Google Pixel speaker',
            'iPhone back cover',
            'Samsung home button',
            'Huawei flex cable',
            'Sony camera module',
            'LG display assembly',
            'Motorola battery',
            'Nokia charging port',
            'iPhone 13 Pro Max',
            'Galaxy Note 20',
        ];

        foreach ($users->take(20) as $user) {
            // Create 5-15 search records per user
            $searchCount = fake()->numberBetween(5, 15);

            for ($i = 0; $i < $searchCount; $i++) {
                UserSearch::create([
                    'user_id' => $user->id,
                    'search_query' => fake()->randomElement($searchQueries),
                    'search_type' => fake()->randomElement(['all', 'category', 'model', 'part']),
                    'results_count' => fake()->numberBetween(0, 50),
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);
            }
        }

        $this->command->info('✓ Created search history for users');
    }

    /**
     * Create user favorites for testing.
     */
    private function createUserFavorites(): void
    {
        $users = User::take(10)->get();
        $parts = Part::take(20)->get();

        foreach ($users as $user) {
            // Create 3-8 favorite parts per user
            $favoriteCount = fake()->numberBetween(3, 8);
            $selectedParts = $parts->random($favoriteCount);

            foreach ($selectedParts as $part) {
                UserFavorite::create([
                    'user_id' => $user->id,
                    'favoritable_type' => Part::class,
                    'favoritable_id' => $part->id,
                    'created_at' => fake()->dateTimeBetween('-60 days', 'now'),
                    'updated_at' => now(),
                ]);
            }
        }

        $this->command->info('✓ Created user favorites for testing');
    }
}
