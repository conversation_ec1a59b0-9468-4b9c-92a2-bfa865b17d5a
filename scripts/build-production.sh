#!/bin/bash

# Production Build Script for Mobile Parts DB
# This script builds the application for production and creates a deployment package

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required commands
print_status "Checking required commands..."
if ! command_exists composer; then
    print_error "Composer is not installed or not in PATH"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

if ! command_exists php; then
    print_error "PHP is not installed or not in PATH"
    exit 1
fi

print_success "All required commands are available"

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/production-deployment"

print_status "Project root: $PROJECT_ROOT"
print_status "Deployment directory: $DEPLOYMENT_DIR"

# Change to project root
cd "$PROJECT_ROOT"

# Step 1: Install production dependencies
print_status "Step 1: Installing production dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction
if [ $? -eq 0 ]; then
    print_success "Production dependencies installed successfully"
else
    print_error "Failed to install production dependencies"
    exit 1
fi

# Step 2: Build assets
print_status "Step 2: Installing npm dependencies..."
npm ci --silent
if [ $? -eq 0 ]; then
    print_success "npm dependencies installed successfully"
else
    print_error "Failed to install npm dependencies"
    exit 1
fi

print_status "Building assets for production..."
npm run build
if [ $? -eq 0 ]; then
    print_success "Assets built successfully"
else
    print_error "Failed to build assets"
    exit 1
fi

# Step 3: Clear all caches to avoid development path issues
print_status "Step 3: Clearing all caches to prevent development path issues..."

print_status "Clearing all caches..."
php artisan optimize:clear
if [ $? -eq 0 ]; then
    print_success "All caches cleared successfully"
else
    print_error "Failed to clear caches"
    exit 1
fi

# DO NOT cache anything during build - all caching should happen on production server
# This prevents development paths from being baked into cached files
print_status "Skipping all caching during build process..."
print_warning "Configuration, routes, views, and events will be cached on production server"
print_warning "This prevents development paths from being included in cached files"

# Step 4: Set proper permissions
print_status "Step 4: Setting proper permissions..."
chmod -R 755 storage bootstrap/cache
if [ $? -eq 0 ]; then
    print_success "Permissions set successfully"
else
    print_error "Failed to set permissions"
    exit 1
fi

# Step 5: Create production deployment directory
print_status "Step 5: Creating production deployment package..."

# Remove existing deployment directory if it exists
if [ -d "$DEPLOYMENT_DIR" ]; then
    print_warning "Removing existing deployment directory..."
    rm -rf "$DEPLOYMENT_DIR"
fi

# Create deployment directory
mkdir -p "$DEPLOYMENT_DIR"

# Copy essential files and directories for production
print_status "Copying files to deployment directory..."

# Core application files
cp -r app "$DEPLOYMENT_DIR/"
cp -r config "$DEPLOYMENT_DIR/"
cp -r database "$DEPLOYMENT_DIR/"
cp -r public "$DEPLOYMENT_DIR/"
cp -r resources "$DEPLOYMENT_DIR/"
cp -r routes "$DEPLOYMENT_DIR/"
cp -r vendor "$DEPLOYMENT_DIR/"

# Copy bootstrap directory but exclude cache files to prevent development path issues
print_status "Copying bootstrap directory (excluding cache files)..."
mkdir -p "$DEPLOYMENT_DIR/bootstrap"
cp -r bootstrap/app.php "$DEPLOYMENT_DIR/bootstrap/" 2>/dev/null || true
cp -r bootstrap/providers.php "$DEPLOYMENT_DIR/bootstrap/" 2>/dev/null || true
# Create empty cache directory with proper structure
mkdir -p "$DEPLOYMENT_DIR/bootstrap/cache"
echo "*" > "$DEPLOYMENT_DIR/bootstrap/cache/.gitignore"
echo "!.gitignore" >> "$DEPLOYMENT_DIR/bootstrap/cache/.gitignore"

# Copy storage directory but exclude cache files
print_status "Copying storage directory (excluding cache files)..."
mkdir -p "$DEPLOYMENT_DIR/storage"
cp -r storage/app "$DEPLOYMENT_DIR/storage/" 2>/dev/null || true
mkdir -p "$DEPLOYMENT_DIR/storage/logs"
# Create framework directories but exclude cached files
mkdir -p "$DEPLOYMENT_DIR/storage/framework/cache"
mkdir -p "$DEPLOYMENT_DIR/storage/framework/sessions"
mkdir -p "$DEPLOYMENT_DIR/storage/framework/views"
mkdir -p "$DEPLOYMENT_DIR/storage/framework/testing"
# Add .gitkeep files
touch "$DEPLOYMENT_DIR/storage/framework/cache/.gitkeep"
touch "$DEPLOYMENT_DIR/storage/framework/sessions/.gitkeep"
touch "$DEPLOYMENT_DIR/storage/framework/views/.gitkeep"
touch "$DEPLOYMENT_DIR/storage/framework/testing/.gitkeep"
touch "$DEPLOYMENT_DIR/storage/logs/.gitkeep"

# Essential root files
cp artisan "$DEPLOYMENT_DIR/"
cp composer.json "$DEPLOYMENT_DIR/"
cp composer.lock "$DEPLOYMENT_DIR/"

# Copy .env.example as a template (production server will need actual .env)
if [ -f ".env.example" ]; then
    cp .env.example "$DEPLOYMENT_DIR/"
fi

# Create a README for deployment
cat > "$DEPLOYMENT_DIR/DEPLOYMENT_README.md" << 'EOF'
# Production Deployment Package

This directory contains all the files needed for production deployment.

## Setup Instructions:

1. Upload all files to your production server
2. Create a `.env` file with your production environment variables
3. Run: `php artisan key:generate`
4. Run: `php artisan migrate --force` (if database changes)
5. **IMPORTANT**: Cache application files on production server with production paths:
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   php artisan event:cache
   ```
6. Set proper file permissions:
   ```bash
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage bootstrap/cache
   ```
7. Point your web server document root to the `public` directory

## Important Notes:

- This package contains NO cached files to prevent development path issues
- Do not run `composer install` or `npm install` on production
- You MUST run caching commands on production server (see step 5 above)
- Make sure to set proper environment variables in `.env`
- Ensure your web server is configured correctly
- All caching happens on production server to use correct production paths

## Files Included:

- All Laravel application files
- Optimized Composer dependencies (vendor/)
- Built and optimized frontend assets (public/build/)
- Clean cache directories (no cached files to prevent path issues)
- Database migrations and seeders

Generated on: $(date)
EOF

# Create deployment info file
cat > "$DEPLOYMENT_DIR/deployment-info.txt" << EOF
Deployment Package Information
==============================

Build Date: $(date)
Build Host: $(hostname)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Not available")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Not available")

PHP Version: $(php --version | head -n 1)
Composer Version: $(composer --version)
Node Version: $(node --version 2>/dev/null || echo "Not available")
NPM Version: $(npm --version 2>/dev/null || echo "Not available")

Package Contents:
- Laravel application files
- Production Composer dependencies
- Built frontend assets
- Clean cache directories (no cached files to prevent development path issues)
- Database structure and seeders

IMPORTANT: Run caching commands on production server after deployment!
EOF

# Calculate deployment package size
PACKAGE_SIZE=$(du -sh "$DEPLOYMENT_DIR" | cut -f1)

print_success "Production deployment package created successfully!"
print_status "Package location: $DEPLOYMENT_DIR"
print_status "Package size: $PACKAGE_SIZE"

# Create a zip archive of the deployment
print_status "Creating ZIP archive..."
cd "$(dirname "$DEPLOYMENT_DIR")"
ZIP_NAME="production-deployment-$(date +%Y%m%d-%H%M%S).zip"
zip -r "$ZIP_NAME" "$(basename "$DEPLOYMENT_DIR")" > /dev/null
if [ $? -eq 0 ]; then
    print_success "ZIP archive created: $ZIP_NAME"
    ZIP_SIZE=$(du -sh "$ZIP_NAME" | cut -f1)
    print_status "Archive size: $ZIP_SIZE"

    # Delete the production-deployment directory after creating ZIP
    print_status "Cleaning up deployment directory..."
    rm -rf "$DEPLOYMENT_DIR"
    print_success "Deployment directory cleaned up"
else
    print_error "Failed to create ZIP archive"
    exit 1
fi
cd "$PROJECT_ROOT"

print_success "Production build completed successfully!"
print_status "Next steps:"
print_status "1. Upload and extract '$ZIP_NAME' on your production server"
print_status "2. Configure your .env file on the production server"
print_status "3. Set up your web server to point to the public directory"
print_status "4. Run any necessary database migrations"
print_warning "5. IMPORTANT: Run these caching commands on production server:"
print_warning "   php artisan config:cache"
print_warning "   php artisan route:cache"
print_warning "   php artisan view:cache"
print_warning "   php artisan event:cache"

echo
print_success "Build process finished!"
print_status "Deployment package: $ZIP_NAME"
